"""
OutlookMaster AI Agent - Core Email Summarization Module
Demonstrates intelligent email analysis with MyTGuy-specific personalization
"""

import json
import re
import sqlite3
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import openai
import os
from pathlib import Path

# Configuration
USER_EMAIL = "<EMAIL>"
MYTGUY_DOMAIN = "mytguy.live"
MYTGUY_PRIORITY_MULTIPLIER = 2.0
MYTGUY_KEYWORDS = ["urgent", "asap", "thoughts", "feedback", "review", "decision", "approve"]

@dataclass
class EmailSummary:
    """Structured email summary with MyTGuy intelligence"""
    sender: str
    subject: str
    priority_score: float
    is_mytguy_related: bool
    summary: str
    action_items: List[str]
    suggested_response_tone: str
    mytguy_insights: Optional[str] = None
    follow_up_needed: bool = False

class MyTGuyAnalyzer:
    """Specialized analyzer for MyTGuy domain communications"""
    
    def __init__(self):
        self.mytguy_patterns = {
            'urgent_indicators': ['asap', 'urgent', 'immediately', 'critical'],
            'decision_requests': ['thoughts', 'opinion', 'decide', 'approve', 'feedback'],
            'meeting_requests': ['schedule', 'meeting', 'call', 'discuss'],
            'status_updates': ['update', 'progress', 'status', 'report']
        }
    
    def is_mytguy_related(self, sender_email: str, subject: str, body: str) -> bool:
        """Determine if email is MyTGuy related"""
        return (
            MYTGUY_DOMAIN in sender_email.lower() or
            'mytguy' in subject.lower() or
            'mytguy' in body.lower()
        )
    
    def analyze_mytguy_context(self, email_body: str, subject: str) -> Dict:
        """Extract MyTGuy-specific insights"""
        insights = {
            'urgency_level': 'normal',
            'request_type': 'information',
            'implicit_asks': [],
            'domain_context': False
        }
        
        email_lower = email_body.lower()
        subject_lower = subject.lower()
        
        # Urgency detection
        urgent_count = sum(1 for pattern in self.mytguy_patterns['urgent_indicators'] 
                          if pattern in email_lower)
        if urgent_count > 0:
            insights['urgency_level'] = 'high'
        
        # Request type classification
        if any(pattern in email_lower for pattern in self.mytguy_patterns['decision_requests']):
            insights['request_type'] = 'decision_needed'
        elif any(pattern in email_lower for pattern in self.mytguy_patterns['meeting_requests']):
            insights['request_type'] = 'meeting_request'
        elif any(pattern in email_lower for pattern in self.mytguy_patterns['status_updates']):
            insights['request_type'] = 'status_update'
        
        # Implicit asks detection
        if 'thoughts' in email_lower:
            insights['implicit_asks'].append('Provide detailed analysis/opinion')
        if 'feedback' in email_lower:
            insights['implicit_asks'].append('Give constructive feedback')
        if 'asap' in email_lower or 'urgent' in email_lower:
            insights['implicit_asks'].append('Prioritize immediate response')
        
        insights['domain_context'] = MYTGUY_DOMAIN in email_lower
        
        return insights

class EmailSummarizer:
    """Main email summarization engine with AI integration"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        if self.api_key:
            openai.api_key = self.api_key
        self.mytguy_analyzer = MyTGuyAnalyzer()
        
    def calculate_priority_score(self, sender: str, subject: str, body: str, 
                               is_mytguy: bool) -> float:
        """Calculate email priority score (0-10)"""
        base_score = 5.0
        
        # MyTGuy domain boost
        if is_mytguy:
            base_score *= MYTGUY_PRIORITY_MULTIPLIER
        
        # Keyword analysis
        high_priority_keywords = ['urgent', 'asap', 'critical', 'important', 'deadline']
        keyword_score = sum(2 for keyword in high_priority_keywords 
                           if keyword in (subject + body).lower())
        
        # Sender importance (simplified)
        if 'ceo' in sender.lower() or 'director' in sender.lower():
            base_score += 2.0
        
        final_score = min(base_score + keyword_score, 10.0)
        return final_score
    
    def extract_action_items(self, email_body: str) -> List[str]:
        """Extract action items from email content"""
        action_patterns = [
            r'(?:please|can you|could you|need to|should|must)\s+([^.!?]+)',
            r'(?:action item|todo|task):\s*([^.!?]+)',
            r'(?:by|before|deadline)\s+([^.!?]+)',
        ]
        
        actions = []
        for pattern in action_patterns:
            matches = re.findall(pattern, email_body, re.IGNORECASE)
            actions.extend([match.strip() for match in matches])
        
        # Clean and deduplicate
        cleaned_actions = list(set([action for action in actions if len(action) > 10]))
        return cleaned_actions[:5]  # Limit to top 5 actions
    
    def generate_ai_summary(self, sender: str, subject: str, body: str, 
                           is_mytguy: bool) -> str:
        """Generate AI-powered email summary"""
        if not self.api_key:
            return self._generate_rule_based_summary(sender, subject, body)
        
        mytguy_context = "This email is from MyTGuy domain - prioritize business context." if is_mytguy else ""
        
        prompt = f"""
        As J. Lewis's email assistant, summarize this email concisely:
        
        From: {sender}
        Subject: {subject}
        
        {mytguy_context}
        
        Email Body:
        {body}
        
        Provide a 2-3 sentence summary focusing on:
        1. Key message/purpose
        2. Any requests or action items
        3. Important deadlines or context
        
        Keep the tone professional but conversational.
        """
        
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=150,
                temperature=0.3
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            print(f"AI summarization failed: {e}")
            return self._generate_rule_based_summary(sender, subject, body)
    
    def _generate_rule_based_summary(self, sender: str, subject: str, body: str) -> str:
        """Fallback rule-based summarization"""
        # Extract first few sentences
        sentences = body.split('.')[:3]
        summary = '. '.join(sentences).strip()
        
        if len(summary) > 200:
            summary = summary[:200] + "..."
        
        return f"Email from {sender.split('@')[0]} regarding {subject}. {summary}"
    
    def suggest_response_tone(self, sender: str, is_mytguy: bool, 
                            mytguy_insights: Dict) -> str:
        """Suggest appropriate response tone"""
        if is_mytguy:
            if mytguy_insights.get('urgency_level') == 'high':
                return "immediate_professional"
            elif mytguy_insights.get('request_type') == 'decision_needed':
                return "analytical_detailed"
            else:
                return "collaborative_friendly"
        else:
            return "professional_courteous"
    
    def summarize_email(self, email_data: Dict) -> EmailSummary:
        """Main summarization method"""
        sender = email_data.get('sender', '')
        subject = email_data.get('subject', '')
        body = email_data.get('body', '')
        
        # MyTGuy analysis
        is_mytguy = self.mytguy_analyzer.is_mytguy_related(sender, subject, body)
        mytguy_insights = self.mytguy_analyzer.analyze_mytguy_context(body, subject) if is_mytguy else {}
        
        # Core analysis
        priority_score = self.calculate_priority_score(sender, subject, body, is_mytguy)
        action_items = self.extract_action_items(body)
        summary = self.generate_ai_summary(sender, subject, body, is_mytguy)
        response_tone = self.suggest_response_tone(sender, is_mytguy, mytguy_insights)
        
        # MyTGuy-specific insights
        mytguy_insight_text = None
        if is_mytguy and mytguy_insights.get('implicit_asks'):
            mytguy_insight_text = f"MyTGuy insights: {', '.join(mytguy_insights['implicit_asks'])}"
        
        return EmailSummary(
            sender=sender,
            subject=subject,
            priority_score=priority_score,
            is_mytguy_related=is_mytguy,
            summary=summary,
            action_items=action_items,
            suggested_response_tone=response_tone,
            mytguy_insights=mytguy_insight_text,
            follow_up_needed=len(action_items) > 0 or mytguy_insights.get('urgency_level') == 'high'
        )

def load_sample_emails() -> List[Dict]:
    """Load sample emails for demonstration"""
    sample_emails = [
        {
            "sender": "<EMAIL>",
            "subject": "Urgent: Project Alpha Review Needed",
            "body": """Hi J. Lewis,
            
            I need your thoughts on the Project Alpha proposal ASAP. The client is expecting our feedback by end of week, and I want to make sure we're aligned on the approach.
            
            Can you review the attached document and let me know your thoughts on:
            1. The technical approach
            2. Timeline feasibility
            3. Resource allocation
            
            This is critical for our Q4 planning, so please prioritize this.
            
            Thanks,
            MyTGuy"""
        },
        {
            "sender": "<EMAIL>",
            "subject": "Meeting Follow-up",
            "body": """Dear J. Lewis,
            
            Thank you for the productive meeting yesterday. As discussed, we'll proceed with the implementation plan and expect the first milestone deliverable by next month.
            
            Please confirm the timeline and let us know if you need any additional resources.
            
            Best regards,
            Client Team"""
        },
        {
            "sender": "<EMAIL>",
            "subject": "System Maintenance Notification",
            "body": """Hello J. Lewis,
            
            This is a scheduled maintenance notification. Our systems will be undergoing maintenance this weekend from 2 AM to 6 AM EST.
            
            No action required on your part.
            
            Best,
            MyTGuy Support Team"""
        }
    ]
    return sample_emails

def main():
    """Demonstration of the email summarization system"""
    print("=== OutlookMaster AI Agent - Email Summarization Demo ===\n")
    
    # Initialize summarizer
    summarizer = EmailSummarizer()
    
    # Load sample emails
    sample_emails = load_sample_emails()
    
    # Process each email
    for i, email in enumerate(sample_emails, 1):
        print(f"--- Email {i} ---")
        print(f"From: {email['sender']}")
        print(f"Subject: {email['subject']}")
        print()
        
        # Generate summary
        summary = summarizer.summarize_email(email)
        
        # Display results
        print(f"Priority Score: {summary.priority_score:.1f}/10")
        print(f"MyTGuy Related: {'Yes' if summary.is_mytguy_related else 'No'}")
        print(f"Summary: {summary.summary}")
        
        if summary.action_items:
            print(f"Action Items:")
            for action in summary.action_items:
                print(f"  • {action}")
        
        if summary.mytguy_insights:
            print(f"MyTGuy Insights: {summary.mytguy_insights}")
        
        print(f"Suggested Response Tone: {summary.suggested_response_tone}")
        print(f"Follow-up Needed: {'Yes' if summary.follow_up_needed else 'No'}")
        print("\n" + "="*60 + "\n")

if __name__ == "__main__":
    main()
