# core/__init__.py
# Core modules for the Email Agent

from .summarizer import Email<PERSON>ummarizer, EmailSummary
from .drafter import MyTGuyDraftingEngine, EmailDraft, DraftType, ToneStyle
from .persona_engine import PersonaEngine, EmailContext
from .mock_data import get_sample_email, get_all_sample_emails
from .task_extractor import TaskExtractor, ExtractedTask, TaskType, TaskPriority
from .mytguy_insights import MyTGuyInsightsEngine, MyTGuyInsight, InsightType
from .smart_scheduler import SmartScheduler, MeetingRequest, SchedulingSuggestion
from .graph_api_client import GraphAPIClient, EmailMonitor, EmailMessage

__all__ = [
    'EmailSummarizer', 'EmailSummary',
    'MyTGuyDraftingEngine', 'EmailDraft', 'DraftType', 'ToneStyle',
    'PersonaEngine', 'EmailContext',
    'get_sample_email', 'get_all_sample_emails',
    'TaskExtractor', 'ExtractedTask', 'TaskType', 'TaskPriority',
    'MyTGuyInsightsEngine', 'MyTGuyInsight', 'InsightType',
    'SmartScheduler', 'MeetingRequest', 'SchedulingSuggestion',
    'GraphAPIClient', 'EmailMonitor', 'EmailMessage'
]

