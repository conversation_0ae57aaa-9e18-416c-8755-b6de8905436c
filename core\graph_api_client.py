#!/usr/bin/env python3
"""
Microsoft Graph API Email Integration Module
Handles OAuth 2.0 authentication and email operations with Outlook
"""

import os
import json
import logging
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import webbrowser
import urllib.parse
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class EmailMessage:
    """Represents an email message from Graph API"""
    id: str
    subject: str
    sender: str
    body: str
    received_datetime: datetime
    is_read: bool
    importance: str
    has_attachments: bool
    conversation_id: str
    
    @classmethod
    def from_graph_data(cls, data: Dict) -> 'EmailMessage':
        """Create EmailMessage from Graph API response data"""
        return cls(
            id=data.get('id', ''),
            subject=data.get('subject', ''),
            sender=data.get('from', {}).get('emailAddress', {}).get('address', ''),
            body=data.get('body', {}).get('content', ''),
            received_datetime=datetime.fromisoformat(data.get('receivedDateTime', '').replace('Z', '+00:00')),
            is_read=data.get('isRead', False),
            importance=data.get('importance', 'normal'),
            has_attachments=data.get('hasAttachments', False),
            conversation_id=data.get('conversationId', '')
        )

class AuthCallbackHandler(BaseHTTPRequestHandler):
    """HTTP handler for OAuth callback"""
    
    def do_GET(self):
        """Handle GET request for OAuth callback"""
        if self.path.startswith('/callback'):
            # Parse the authorization code from the callback URL
            parsed_url = urllib.parse.urlparse(self.path)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            
            if 'code' in query_params:
                self.server.auth_code = query_params['code'][0]
                self.send_response(200)
                self.send_header('Content-type', 'text/html')
                self.end_headers()
                self.wfile.write(b"""
                <html>
                <body>
                <h1>Authentication Successful!</h1>
                <p>You can close this window and return to the application.</p>
                </body>
                </html>
                """)
            else:
                self.send_response(400)
                self.send_header('Content-type', 'text/html')
                self.end_headers()
                self.wfile.write(b"""
                <html>
                <body>
                <h1>Authentication Failed!</h1>
                <p>No authorization code received.</p>
                </body>
                </html>
                """)
        else:
            self.send_response(404)
            self.end_headers()
    
    def log_message(self, format, *args):
        """Suppress default logging"""
        pass

class GraphAPIClient:
    """Microsoft Graph API client for email operations"""
    
    def __init__(self, client_id: str, client_secret: str, tenant_id: str = "common"):
        """Initialize Graph API client"""
        self.client_id = client_id
        self.client_secret = client_secret
        self.tenant_id = tenant_id
        self.redirect_uri = "http://localhost:8080/callback"
        
        # OAuth endpoints
        self.authority = f"https://login.microsoftonline.com/{tenant_id}"
        self.auth_endpoint = f"{self.authority}/oauth2/v2.0/authorize"
        self.token_endpoint = f"{self.authority}/oauth2/v2.0/token"
        
        # Graph API endpoints
        self.graph_endpoint = "https://graph.microsoft.com/v1.0"
        
        # Required scopes for email operations
        self.scopes = [
            "https://graph.microsoft.com/Mail.Read",
            "https://graph.microsoft.com/Mail.Send",
            "https://graph.microsoft.com/Mail.ReadWrite",
            "https://graph.microsoft.com/User.Read"
        ]
        
        # Token storage
        self.access_token = None
        self.refresh_token = None
        self.token_expires_at = None
    
    def authenticate(self) -> bool:
        """Perform OAuth 2.0 authentication flow"""
        try:
            logger.info("Starting OAuth 2.0 authentication flow...")
            
            # Step 1: Get authorization code
            auth_code = self._get_authorization_code()
            if not auth_code:
                logger.error("Failed to get authorization code")
                return False
            
            # Step 2: Exchange authorization code for access token
            token_data = self._get_access_token(auth_code)
            if not token_data:
                logger.error("Failed to get access token")
                return False
            
            # Store token information
            self.access_token = token_data.get('access_token')
            self.refresh_token = token_data.get('refresh_token')
            expires_in = token_data.get('expires_in', 3600)
            self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
            
            logger.info("Authentication successful!")
            return True
            
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return False
    
    def _get_authorization_code(self) -> Optional[str]:
        """Get authorization code via browser-based OAuth flow"""
        try:
            # Build authorization URL
            auth_params = {
                'client_id': self.client_id,
                'response_type': 'code',
                'redirect_uri': self.redirect_uri,
                'scope': ' '.join(self.scopes),
                'response_mode': 'query',
                'state': 'email_agent_auth'
            }
            
            auth_url = f"{self.auth_endpoint}?" + urllib.parse.urlencode(auth_params)
            
            # Start local HTTP server to handle callback
            server = HTTPServer(('localhost', 8080), AuthCallbackHandler)
            server.auth_code = None
            
            # Start server in background thread
            server_thread = threading.Thread(target=server.serve_forever)
            server_thread.daemon = True
            server_thread.start()
            
            # Open browser for authentication
            logger.info("Opening browser for authentication...")
            webbrowser.open(auth_url)
            
            # Wait for callback with timeout
            timeout = 300  # 5 minutes
            start_time = time.time()
            
            while server.auth_code is None and (time.time() - start_time) < timeout:
                time.sleep(1)
            
            # Shutdown server
            server.shutdown()
            
            return server.auth_code
            
        except Exception as e:
            logger.error(f"Failed to get authorization code: {e}")
            return None
    
    def _get_access_token(self, auth_code: str) -> Optional[Dict]:
        """Exchange authorization code for access token"""
        try:
            token_data = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'code': auth_code,
                'redirect_uri': self.redirect_uri,
                'grant_type': 'authorization_code'
            }
            
            response = requests.post(self.token_endpoint, data=token_data)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"Failed to get access token: {e}")
            return None
    
    def _refresh_access_token(self) -> bool:
        """Refresh access token using refresh token"""
        try:
            if not self.refresh_token:
                return False
            
            token_data = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'refresh_token': self.refresh_token,
                'grant_type': 'refresh_token'
            }
            
            response = requests.post(self.token_endpoint, data=token_data)
            response.raise_for_status()
            
            token_response = response.json()
            self.access_token = token_response.get('access_token')
            expires_in = token_response.get('expires_in', 3600)
            self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
            
            logger.info("Access token refreshed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to refresh access token: {e}")
            return False
    
    def _ensure_valid_token(self) -> bool:
        """Ensure we have a valid access token"""
        if not self.access_token:
            return False
        
        # Check if token is expired (with 5 minute buffer)
        if self.token_expires_at and datetime.now() >= (self.token_expires_at - timedelta(minutes=5)):
            logger.info("Access token expired, attempting refresh...")
            return self._refresh_access_token()
        
        return True
    
    def _make_graph_request(self, endpoint: str, method: str = 'GET', data: Dict = None) -> Optional[Dict]:
        """Make authenticated request to Graph API"""
        try:
            if not self._ensure_valid_token():
                logger.error("No valid access token available")
                return None
            
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            
            url = f"{self.graph_endpoint}{endpoint}"
            
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=headers, json=data)
            elif method.upper() == 'PATCH':
                response = requests.patch(url, headers=headers, json=data)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"Graph API request failed: {e}")
            logger.error(f"Response: {e.response.text if e.response else 'No response'}")
            return None
        except Exception as e:
            logger.error(f"Graph API request error: {e}")
            return None
    
    def get_messages(self, folder: str = "inbox", limit: int = 50, unread_only: bool = False) -> List[EmailMessage]:
        """Get email messages from specified folder"""
        try:
            # Build endpoint
            endpoint = f"/me/mailFolders/{folder}/messages"
            
            # Build query parameters
            params = {
                '$top': limit,
                '$orderby': 'receivedDateTime desc'
            }
            
            if unread_only:
                params['$filter'] = 'isRead eq false'
            
            # Add query parameters to endpoint
            query_string = urllib.parse.urlencode(params)
            endpoint = f"{endpoint}?{query_string}"
            
            # Make request
            response = self._make_graph_request(endpoint)
            if not response:
                return []
            
            # Convert to EmailMessage objects
            messages = []
            for item in response.get('value', []):
                try:
                    message = EmailMessage.from_graph_data(item)
                    messages.append(message)
                except Exception as e:
                    logger.warning(f"Failed to parse message: {e}")
                    continue
            
            logger.info(f"Retrieved {len(messages)} messages from {folder}")
            return messages
            
        except Exception as e:
            logger.error(f"Failed to get messages: {e}")
            return []
    
    def send_message(self, to_email: str, subject: str, body: str, cc_emails: List[str] = None) -> bool:
        """Send an email message"""
        try:
            # Build message data
            message_data = {
                "message": {
                    "subject": subject,
                    "body": {
                        "contentType": "HTML",
                        "content": body
                    },
                    "toRecipients": [
                        {
                            "emailAddress": {
                                "address": to_email
                            }
                        }
                    ]
                }
            }
            
            # Add CC recipients if provided
            if cc_emails:
                message_data["message"]["ccRecipients"] = [
                    {"emailAddress": {"address": email}} for email in cc_emails
                ]
            
            # Send message
            response = self._make_graph_request("/me/sendMail", method='POST', data=message_data)
            
            if response is not None:  # sendMail returns 202 with no content on success
                logger.info(f"Email sent successfully to {to_email}")
                return True
            else:
                logger.error("Failed to send email")
                return False
                
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            return False
    
    def mark_as_read(self, message_id: str) -> bool:
        """Mark a message as read"""
        try:
            data = {"isRead": True}
            response = self._make_graph_request(f"/me/messages/{message_id}", method='PATCH', data=data)
            
            if response:
                logger.info(f"Message {message_id} marked as read")
                return True
            else:
                logger.error(f"Failed to mark message {message_id} as read")
                return False
                
        except Exception as e:
            logger.error(f"Failed to mark message as read: {e}")
            return False
    
    def get_user_profile(self) -> Optional[Dict]:
        """Get current user's profile information"""
        try:
            response = self._make_graph_request("/me")
            if response:
                logger.info(f"Retrieved profile for user: {response.get('displayName', 'Unknown')}")
            return response
            
        except Exception as e:
            logger.error(f"Failed to get user profile: {e}")
            return None
    
    def save_credentials(self, filepath: str):
        """Save authentication credentials to file"""
        try:
            credentials = {
                'access_token': self.access_token,
                'refresh_token': self.refresh_token,
                'token_expires_at': self.token_expires_at.isoformat() if self.token_expires_at else None,
                'client_id': self.client_id,
                'tenant_id': self.tenant_id
            }
            
            with open(filepath, 'w') as f:
                json.dump(credentials, f, indent=2)
            
            logger.info(f"Credentials saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Failed to save credentials: {e}")
    
    def load_credentials(self, filepath: str) -> bool:
        """Load authentication credentials from file"""
        try:
            if not os.path.exists(filepath):
                return False
            
            with open(filepath, 'r') as f:
                credentials = json.load(f)
            
            self.access_token = credentials.get('access_token')
            self.refresh_token = credentials.get('refresh_token')
            
            expires_str = credentials.get('token_expires_at')
            if expires_str:
                self.token_expires_at = datetime.fromisoformat(expires_str)
            
            logger.info(f"Credentials loaded from {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load credentials: {e}")
            return False

class EmailMonitor:
    """Email monitoring service using Graph API"""
    
    def __init__(self, graph_client: GraphAPIClient):
        self.graph_client = graph_client
        self.last_check = datetime.now()
        self.monitoring = False
        self.monitor_thread = None
        self.check_interval = 60  # seconds
        
        # Callbacks for email events
        self.new_email_callback = None
        self.email_processed_callback = None
    
    def set_new_email_callback(self, callback):
        """Set callback function for new emails"""
        self.new_email_callback = callback
    
    def set_email_processed_callback(self, callback):
        """Set callback function for processed emails"""
        self.email_processed_callback = callback
    
    def start_monitoring(self):
        """Start email monitoring in background thread"""
        if self.monitoring:
            logger.warning("Email monitoring is already running")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        logger.info("Email monitoring started")
    
    def stop_monitoring(self):
        """Stop email monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        logger.info("Email monitoring stopped")
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                # Get new messages since last check
                new_messages = self._get_new_messages()
                
                # Process new messages
                for message in new_messages:
                    if self.new_email_callback:
                        try:
                            self.new_email_callback(message)
                            
                            if self.email_processed_callback:
                                self.email_processed_callback(message)
                                
                        except Exception as e:
                            logger.error(f"Error processing email {message.id}: {e}")
                
                # Update last check time
                self.last_check = datetime.now()
                
                # Wait before next check
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.check_interval)
    
    def _get_new_messages(self) -> List[EmailMessage]:
        """Get messages received since last check"""
        try:
            # Get recent messages
            messages = self.graph_client.get_messages(limit=20)
            
            # Filter for messages newer than last check
            new_messages = [
                msg for msg in messages 
                if msg.received_datetime > self.last_check
            ]
            
            if new_messages:
                logger.info(f"Found {len(new_messages)} new messages")
            
            return new_messages
            
        except Exception as e:
            logger.error(f"Failed to get new messages: {e}")
            return []

def main():
    """Demonstration of Graph API email integration"""
    print("=== Microsoft Graph API Email Integration Demo ===\n")
    
    # Note: These would normally come from environment variables or config
    client_id = os.getenv('AZURE_CLIENT_ID', 'your-client-id-here')
    client_secret = os.getenv('AZURE_CLIENT_SECRET', 'your-client-secret-here')
    tenant_id = os.getenv('AZURE_TENANT_ID', 'common')
    
    if client_id == 'your-client-id-here':
        print("Please set up Azure app registration and configure environment variables:")
        print("- AZURE_CLIENT_ID")
        print("- AZURE_CLIENT_SECRET")
        print("- AZURE_TENANT_ID (optional, defaults to 'common')")
        return
    
    # Initialize Graph API client
    graph_client = GraphAPIClient(client_id, client_secret, tenant_id)
    
    # Try to load saved credentials
    credentials_file = "data/graph_credentials.json"
    if not graph_client.load_credentials(credentials_file):
        print("No saved credentials found. Starting authentication flow...")
        
        # Authenticate
        if not graph_client.authenticate():
            print("Authentication failed!")
            return
        
        # Save credentials for future use
        os.makedirs("data", exist_ok=True)
        graph_client.save_credentials(credentials_file)
    
    # Test API access
    print("Testing Graph API access...")
    
    # Get user profile
    profile = graph_client.get_user_profile()
    if profile:
        print(f"Authenticated as: {profile.get('displayName')} ({profile.get('mail')})")
    
    # Get recent messages
    print("\nFetching recent messages...")
    messages = graph_client.get_messages(limit=5)
    
    if messages:
        print(f"Found {len(messages)} recent messages:")
        for i, msg in enumerate(messages, 1):
            print(f"{i}. From: {msg.sender}")
            print(f"   Subject: {msg.subject}")
            print(f"   Received: {msg.received_datetime}")
            print(f"   Read: {'Yes' if msg.is_read else 'No'}")
            print()
    else:
        print("No messages found or failed to retrieve messages.")
    
    # Demonstrate email monitoring
    print("Starting email monitoring for 30 seconds...")
    
    def on_new_email(message: EmailMessage):
        print(f"NEW EMAIL: {message.subject} from {message.sender}")
    
    monitor = EmailMonitor(graph_client)
    monitor.set_new_email_callback(on_new_email)
    monitor.start_monitoring()
    
    # Monitor for 30 seconds
    time.sleep(30)
    monitor.stop_monitoring()
    
    print("Email monitoring demo completed.")

if __name__ == "__main__":
    main()

