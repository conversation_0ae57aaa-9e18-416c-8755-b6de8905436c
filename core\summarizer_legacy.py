# core/summarizer.py
# Main Email Summarization Engine

import re
from typing import Dict, List, Optional
from datetime import datetime
from dataclasses import dataclass

from persona_engine import PersonaEng<PERSON>, EmailContext
from config import AgentConfig

@dataclass
class EmailSummary:
    """Container for processed email summary"""
    original_subject: str
    sender: str
    summary: str
    key_points: List[str]
    action_items: List[str]
    priority_score: int
    priority_reasons: List[str]
    recommended_actions: List[str]
    mytguy_insights: Optional[Dict] = None
    sentiment: str = "neutral"
    estimated_response_time: str = "within 24 hours"

class ContextualSummarizer:
    """Advanced email summarization with personalization"""
    
    def __init__(self):
        self.config = AgentConfig()
        self.persona_engine = PersonaEngine()
        
    def summarize_email(self, sender: str, subject: str, body: str, 
                       timestamp: Optional[datetime] = None) -> EmailSummary:
        """Main summarization method"""
        
        # Create email context
        email_context = EmailContext(
            sender=sender,
            subject=subject,
            body=body,
            timestamp=timestamp or datetime.now()
        )
        
        # Generate personalized insights
        insights = self.persona_engine.generate_personalized_insights(email_context)
        
        # Get summarization style based on sender
        style = self.persona_engine.get_summarization_style(insights["sender_type"])
        
        # Generate core summary
        summary = self._generate_core_summary(email_context, style)
        
        # Extract key points and action items
        key_points = self._extract_key_points(email_context, style)
        action_items = self._extract_action_items(email_context)
        
        # Analyze sentiment
        sentiment = self._analyze_sentiment(body)
        
        # Estimate response timing
        response_time = self._estimate_response_time(insights["priority_score"], 
                                                   insights["sender_type"])
        
        return EmailSummary(
            original_subject=subject,
            sender=sender,
            summary=summary,
            key_points=key_points,
            action_items=action_items,
            priority_score=insights["priority_score"],
            priority_reasons=insights["priority_reasons"],
            recommended_actions=insights["recommended_actions"],
            mytguy_insights=insights.get("mytguy_analysis"),
            sentiment=sentiment,
            estimated_response_time=response_time
        )
    
    def _generate_core_summary(self, email_context: EmailContext, style: Dict) -> str:
        """Generate the main email summary"""
        body = email_context.body
        
        # Clean up the email body
        cleaned_body = self._clean_email_body(body)
        
        # Extract main themes
        sentences = re.split(r'[.!?]+', cleaned_body)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
        
        # Simple extractive summarization (in production, you'd use AI/NLP)
        if len(sentences) <= 3:
            summary = cleaned_body
        else:
            # Take first sentence, middle sentence, and any sentence with key terms
            key_sentences = []
            
            # Always include first sentence
            if sentences:
                key_sentences.append(sentences[0])
            
            # Look for sentences with important keywords
            important_keywords = ["decision", "meeting", "deadline", "urgent", "need", "request"]
            for sentence in sentences[1:]:
                if any(keyword in sentence.lower() for keyword in important_keywords):
                    key_sentences.append(sentence)
                    if len(key_sentences) >= 3:
                        break
            
            # If we still need more content, add the last sentence
            if len(key_sentences) < 2 and len(sentences) > 1:
                key_sentences.append(sentences[-1])
            
            summary = ". ".join(key_sentences) + "."
        
        # Limit length based on preferences
        max_length = 300 if style["length"] == "brief" else 500 if style["length"] == "medium" else 800
        if len(summary) > max_length:
            summary = summary[:max_length] + "..."
        
        return summary
    
    def _extract_key_points(self, email_context: EmailContext, style: Dict) -> List[str]:
        """Extract key points from email"""
        body = email_context.body.lower()
        key_points = []
        
        # Look for numbered/bulleted lists
        bullet_pattern = r'(?:^|\n)[•\-\*\d+\.]\s*(.+?)(?=\n|$)'
        bullets = re.findall(bullet_pattern, email_context.body, re.MULTILINE)
        key_points.extend(bullets[:3])  # Limit to top 3
        
        # Extract questions
        questions = re.findall(r'([^.!?]*\?)', email_context.body)
        key_points.extend([q.strip() for q in questions if len(q.strip()) > 10][:2])
        
        # Look for key phrases
        key_phrases = [
            "important to note",
            "please note",
            "key point",
            "main concern",
            "primary issue"
        ]
        
        for phrase in key_phrases:
            if phrase in body:
                # Extract sentence containing the phrase
                sentences = email_context.body.split('.')
                for sentence in sentences:
                    if phrase in sentence.lower():
                        key_points.append(sentence.strip())
                        break
        
        return list(set(key_points))[:5]  # Remove duplicates, limit to 5
    
    def _extract_action_items(self, email_context: EmailContext) -> List[str]:
        """Extract actionable items from email"""
        body = email_context.body.lower()
        action_items = []
        
        # Action verbs and patterns
        action_patterns = [
            r'(?:please|can you|could you|need to|should|must)\s+([^.!?]+)',
            r'(?:action item|to do|follow up):\s*([^.!?]+)',
            r'(?:deadline|due|by)\s+([^.!?]+)'
        ]
        
        for pattern in action_patterns:
            matches = re.findall(pattern, body, re.IGNORECASE)
            action_items.extend([match.strip() for match in matches if len(match.strip()) > 5])
        
        # Look for imperative sentences
        sentences = email_context.body.split('.')
        imperative_starters = ['please', 'can you', 'could you', 'need you to', 'would you']
        
        for sentence in sentences:
            sentence_lower = sentence.strip().lower()
            if any(sentence_lower.startswith(starter) for starter in imperative_starters):
                action_items.append(sentence.strip())
        
        return list(set(action_items))[:4]  # Remove duplicates, limit to 4
    
    def _analyze_sentiment(self, body: str) -> str:
        """Simple sentiment analysis"""
        body = body.lower()
        
        positive_words = ['thank', 'great', 'excellent', 'appreciate', 'good', 'pleased']
        negative_words = ['problem', 'issue', 'concern', 'urgent', 'worried', 'disappointed']
        neutral_words = ['update', 'meeting', 'schedule', 'information', 'follow up']
        
        positive_count = sum(1 for word in positive_words if word in body)
        negative_count = sum(1 for word in negative_words if word in body)
        
        if positive_count > negative_count and positive_count > 0:
            return "positive"
        elif negative_count > positive_count and negative_count > 0:
            return "negative"
        else:
            return "neutral"
    
    def _estimate_response_time(self, priority_score: int, sender_type: str) -> str:
        """Estimate appropriate response timeframe"""
        if sender_type == "mytguy":
            if priority_score >= 8:
                return "within 2 hours"
            elif priority_score >= 6:
                return "within 4 hours"
            else:
                return "within 8 hours"
        else:
            if priority_score >= 9:
                return "within 4 hours"
            elif priority_score >= 7:
                return "within 8 hours"
            elif priority_score >= 5:
                return "within 24 hours"
            else:
                return "within 48 hours"
    
    def _clean_email_body(self, body: str) -> str:
        """Clean email body of signatures, quotes, etc."""
        # Remove email signatures (simple heuristic)
        lines = body.split('\n')
        cleaned_lines = []
        
        signature_indicators = ['best regards', 'sincerely', 'thanks', 'sent from', '---']
        found_signature = False
        
        for line in lines:
            line_lower = line.lower().strip()
            if any(indicator in line_lower for indicator in signature_indicators) and len(line_lower) < 50:
                found_signature = True
            if not found_signature:
                cleaned_lines.append(line)
        
        cleaned_body = '\n'.join(cleaned_lines)
        
        # Remove quoted text (lines starting with >)
        lines = cleaned_body.split('\n')
        non_quoted_lines = [line for line in lines if not line.strip().startswith('>')]
        
        return '\n'.join(non_quoted_lines).strip()