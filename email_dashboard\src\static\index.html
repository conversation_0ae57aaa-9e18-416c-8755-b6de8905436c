<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MyTGuy Email Agent Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .status-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-indicator.running {
            background: #27ae60;
        }

        .status-indicator.stopped {
            background: #e74c3c;
        }

        .status-indicator.warning {
            background: #f39c12;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3em;
            font-weight: 600;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            color: #7f8c8d;
            font-weight: 500;
        }

        .metric-value {
            color: #2c3e50;
            font-weight: 700;
            font-size: 1.1em;
        }

        .email-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .email-item {
            padding: 15px;
            border-bottom: 1px solid #ecf0f1;
            transition: background-color 0.3s ease;
        }

        .email-item:hover {
            background-color: #f8f9fa;
        }

        .email-item:last-child {
            border-bottom: none;
        }

        .email-subject {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .email-sender {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 5px;
        }

        .email-meta {
            display: flex;
            gap: 15px;
            font-size: 0.8em;
            color: #95a5a6;
        }

        .priority-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .priority-high {
            background: #e74c3c;
            color: white;
        }

        .priority-medium {
            background: #f39c12;
            color: white;
        }

        .priority-low {
            background: #27ae60;
            color: white;
        }

        .chart-container {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .hidden {
            display: none;
        }

        /* The switch - the box around the slider */
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        /* Hide default HTML checkbox */
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        /* The slider */
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: #2196F3;
        }

        input:focus + .slider {
            box-shadow: 0 0 1px #2196F3;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        /* Rounded sliders */
        .slider.round {
            border-radius: 34px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        .pending-response {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .pending-response-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 10px;
        }

        .pending-response-actions {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }

        .response-preview {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 0.9em;
            max-height: 200px;
            overflow-y: auto;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .status-bar {
                flex-direction: column;
                align-items: stretch;
            }

            .controls {
                justify-content: center;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MyTGuy Email Agent</h1>
            <p>Intelligent Email Management & Automation Dashboard</p>
        </div>

        <div id="alerts"></div>

        <div class="status-bar">
            <div class="status-item">
                <div id="status-indicator" class="status-indicator stopped"></div>
                <span id="status-text">Stopped</span>
            </div>
            <div class="status-item">
                <span>Last Check: <span id="last-check">Never</span></span>
            </div>
            <div class="status-item">
                <span>Processed: <span id="total-processed">0</span> emails</span>
            </div>
            <div class="controls">
                <button id="start-btn" class="btn btn-success">Start Processing</button>
                <button id="stop-btn" class="btn btn-danger" disabled>Stop Processing</button>
                <button id="auth-btn" class="btn btn-warning">Authenticate</button>
                <button id="demo-btn" class="btn btn-primary">Run Demo</button>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="card">
                <h3>Processing Statistics</h3>
                <div id="stats-content">
                    <div class="metric">
                        <span class="metric-label">Total Processed</span>
                        <span class="metric-value" id="stat-total">0</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Auto Responses Sent</span>
                        <span class="metric-value" id="stat-responses">0</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Tasks Created</span>
                        <span class="metric-value" id="stat-tasks">0</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Meetings Processed</span>
                        <span class="metric-value" id="stat-meetings">0</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">High Priority Escalations</span>
                        <span class="metric-value" id="stat-escalations">0</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>Recent Email Activity</h3>
                <div id="recent-emails" class="email-list">
                    <div style="text-align: center; color: #7f8c8d; padding: 20px;">
                        No recent emails processed
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>Priority Distribution</h3>
                <div id="priority-chart" class="chart-container">
                    <div style="text-align: center; color: #7f8c8d;">
                        No data available
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>System Configuration</h3>
                <div id="config-content">
                    <div class="metric">
                        <span class="metric-label">User Email</span>
                        <span class="metric-value" id="config-email">Loading...</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Auto Response</span>
                        <span class="metric-value" id="config-auto-response">Loading...</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Monitor Interval</span>
                        <span class="metric-value" id="config-interval">Loading...</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Mark as Read</span>
                        <span class="metric-value" id="config-mark-read">Loading...</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Auto Response</span>
                        <label class="switch">
                            <input type="checkbox" id="auto-response-toggle" disabled>
                            <span class="slider round"></span>
                        </label>
                        <small style="color: #e74c3c; display: block; margin-top: 5px;">Disabled - Approval Required</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="card" style="width: 100%; max-width: none;">
            <h3>Pending Email Responses <span id="pending-count" class="priority-badge priority-high" style="margin-left: 10px;">0</span></h3>
            <div id="pending-responses" class="email-list">
                <div style="text-align: center; color: #7f8c8d; padding: 20px;">
                    No pending responses awaiting approval
                </div>
            </div>
        </div>
    </div>

    <script>
        class EmailAgentDashboard {
            constructor() {
                this.apiBase = '/api/agent';
                this.refreshInterval = 5000; // 5 seconds
                this.refreshTimer = null;
                
                this.initializeEventListeners();
                this.loadInitialData();
                this.startAutoRefresh();
            }

            initializeEventListeners() {
                document.getElementById('start-btn').addEventListener('click', () => this.startProcessing());
                document.getElementById('stop-btn').addEventListener('click', () => this.stopProcessing());
                document.getElementById('auth-btn').addEventListener('click', () => this.authenticate());
                document.getElementById('demo-btn').addEventListener('click', () => this.runDemo());
                document.getElementById('auto-response-toggle').addEventListener('change', () => this.toggleAutoResponse());
            }

           async toggleAutoResponse() {
                const autoResponseToggle = document.getElementById('auto-response-toggle');
                const autoResponseEnabled = autoResponseToggle.checked;

                try {
                    await this.apiCall('/config', 'POST', {
                        email_processing: {
                            auto_response_enabled: autoResponseEnabled
                        }
                    });
                    this.showAlert('success', 'Auto response setting updated successfully.');
                    await this.updateConfig(); // Refresh config display
                } catch (error) {
                    console.error('Failed to update auto response setting:', error);
                    this.showAlert('error', 'Failed to update auto response setting.');
                    autoResponseToggle.checked = !autoResponseEnabled; // Revert toggle on error
                }
            }

            async apiCall(endpoint, method = 'GET', data = null) {
                try {
                    const options = {
                        method,
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    };

                    if (data) {
                        options.body = JSON.stringify(data);
                    }

                    const response = await fetch(`${this.apiBase}${endpoint}`, options);
                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.error || 'API call failed');
                    }

                    return result;
                } catch (error) {
                    console.error('API call failed:', error);
                    this.showAlert('error', `API Error: ${error.message}`);
                    throw error;
                }
            }

            showAlert(type, message) {
                const alertsContainer = document.getElementById('alerts');
                const alert = document.createElement('div');
                alert.className = `alert alert-${type}`;
                alert.textContent = message;
                
                alertsContainer.appendChild(alert);
                
                setTimeout(() => {
                    alert.remove();
                }, 5000);
            }

            async loadInitialData() {
                try {
                    await Promise.all([
                        this.updateStatus(),
                        this.updateConfig(),
                        this.updateRecentEmails(),
                        this.updateAnalytics(),
                        this.updatePendingResponses()
                    ]);
                } catch (error) {
                    console.error('Failed to load initial data:', error);
                }
            }

            async updateStatus() {
                try {
                    const status = await this.apiCall('/status');
                    
                    // Update status indicator
                    const indicator = document.getElementById('status-indicator');
                    const statusText = document.getElementById('status-text');
                    
                    if (status.is_running) {
                        indicator.className = 'status-indicator running';
                        statusText.textContent = 'Running';
                        document.getElementById('start-btn').disabled = true;
                        document.getElementById('stop-btn').disabled = false;
                    } else {
                        indicator.className = 'status-indicator stopped';
                        statusText.textContent = 'Stopped';
                        document.getElementById('start-btn').disabled = false;
                        document.getElementById('stop-btn').disabled = true;
                    }

                    // Update other status info
                    document.getElementById('last-check').textContent = 
                        status.last_check ? new Date(status.last_check).toLocaleString() : 'Never';
                    document.getElementById('total-processed').textContent = status.total_processed || 0;

                    // Update statistics if available
                    if (status.auto_responses_sent !== undefined) {
                        document.getElementById('stat-total').textContent = status.total_processed || 0;
                        document.getElementById('stat-responses').textContent = status.auto_responses_sent || 0;
                        document.getElementById('stat-tasks').textContent = status.tasks_created || 0;
                        document.getElementById('stat-meetings').textContent = status.meetings_processed || 0;
                        document.getElementById('stat-escalations').textContent = status.high_priority_escalations || 0;
                    }

                } catch (error) {
                    console.error('Failed to update status:', error);
                }
            }

            async updateConfig() {
                try {
                    const config = await this.apiCall('/config');
                    
                    document.getElementById('config-email').textContent = config.user_email || 'Not configured';
                    document.getElementById('config-interval').textContent = 
                        `${config.email_processing?.monitor_interval || 60}s`;
                    document.getElementById('config-mark-read').textContent = 
                        config.email_processing?.mark_processed_as_read ? 'Yes' : 'No';
                    document.getElementById('auto-response-toggle').checked = config.email_processing?.auto_response_enabled || false;

                } catch (error) {
                    console.error('Failed to update config:', error);
                }
            }

            async updateRecentEmails() {
                try {
                    const data = await this.apiCall('/recent-emails');
                    const container = document.getElementById('recent-emails');
                    
                    if (data.emails && data.emails.length > 0) {
                        container.innerHTML = data.emails.map(email => `
                            <div class="email-item">
                                <div class="email-subject">${this.escapeHtml(email.subject)}</div>
                                <div class="email-sender">From: ${this.escapeHtml(email.sender)}</div>
                                <div class="email-meta">
                                    <span class="priority-badge priority-${this.getPriorityClass(email.priority_score)}">
                                        Priority: ${email.priority_score.toFixed(1)}
                                    </span>
                                    <span>Tasks: ${email.tasks_count}</span>
                                    <span>Insights: ${email.insights_count}</span>
                                    <span>Meetings: ${email.meeting_requests_count}</span>
                                </div>
                            </div>
                        `).join('');
                    } else {
                        container.innerHTML = '<div style="text-align: center; color: #7f8c8d; padding: 20px;">No recent emails processed</div>';
                    }

                } catch (error) {
                    console.error('Failed to update recent emails:', error);
                }
            }

            async updateAnalytics() {
                try {
                    const analytics = await this.apiCall('/analytics');
                    
                    // Update priority distribution chart (simplified text display)
                    const chartContainer = document.getElementById('priority-chart');
                    
                    if (analytics.priority_distribution && Object.keys(analytics.priority_distribution).length > 0) {
                        const chartHtml = Object.entries(analytics.priority_distribution)
                            .map(([range, count]) => `
                                <div class="metric">
                                    <span class="metric-label">Priority ${range}</span>
                                    <span class="metric-value">${count}</span>
                                </div>
                            `).join('');
                        
                        chartContainer.innerHTML = chartHtml;
                    } else {
                        chartContainer.innerHTML = '<div style="text-align: center; color: #7f8c8d;">No data available</div>';
                    }

                } catch (error) {
                    console.error('Failed to update analytics:', error);
                }
            }

            async startProcessing() {
                try {
                    const result = await this.apiCall('/start', 'POST');
                    this.showAlert('success', result.message);
                    await this.updateStatus();
                } catch (error) {
                    if (error.message.includes('auth_required')) {
                        this.showAlert('warning', 'Authentication required. Please authenticate first.');
                    }
                }
            }

            async stopProcessing() {
                try {
                    const result = await this.apiCall('/stop', 'POST');
                    this.showAlert('success', result.message);
                    await this.updateStatus();
                } catch (error) {
                    // Error already handled in apiCall
                }
            }

            async authenticate() {
                try {
                    this.showAlert('warning', 'Starting authentication flow. Please check your browser...');
                    const result = await this.apiCall('/authenticate', 'POST');
                    this.showAlert('success', result.message);
                } catch (error) {
                    // Error already handled in apiCall
                }
            }

            async runDemo() {
                try {
                    this.showAlert('warning', 'Running demo with sample emails...');
                    const result = await this.apiCall('/test-demo', 'POST');
                    this.showAlert('success', `Demo completed! Processed ${result.results.length} sample emails.`);
                    
                    // Refresh data after demo
                    setTimeout(() => {
                        this.loadInitialData();
                    }, 1000);
                } catch (error) {
                    // Error already handled in apiCall
                }
            }

            async updatePendingResponses() {
                try {
                    const data = await this.apiCall('/pending-responses');
                    const container = document.getElementById('pending-responses');
                    const countBadge = document.getElementById('pending-count');
                    
                    countBadge.textContent = data.count || 0;
                    
                    if (data.pending_responses && data.pending_responses.length > 0) {
                        container.innerHTML = data.pending_responses.map(response => `
                            <div class="pending-response">
                                <div class="pending-response-header">
                                    <div>
                                        <div class="email-subject">Re: ${this.escapeHtml(response.original_subject)}</div>
                                        <div class="email-sender">To: ${this.escapeHtml(response.to_email)}</div>
                                        <div class="email-meta">
                                            <span class="priority-badge priority-${this.getPriorityClass(response.priority_score)}">
                                                Priority: ${response.priority_score.toFixed(1)}
                                            </span>
                                            <span>Created: ${new Date(response.created_at).toLocaleString()}</span>
                                        </div>
                                    </div>
                                    <div class="pending-response-actions">
                                        <button class="btn btn-success btn-small" onclick="dashboard.approveResponse('${response.id}')">Approve</button>
                                        <button class="btn btn-danger btn-small" onclick="dashboard.rejectResponse('${response.id}')">Reject</button>
                                    </div>
                                </div>
                                <div class="response-preview">${response.body}</div>
                            </div>
                        `).join('');
                    } else {
                        container.innerHTML = '<div style="text-align: center; color: #7f8c8d; padding: 20px;">No pending responses awaiting approval</div>';
                    }
                } catch (error) {
                    console.error('Failed to update pending responses:', error);
                }
            }

            async approveResponse(responseId) {
                try {
                    const result = await this.apiCall(`/pending-responses/${responseId}/approve`, 'POST');
                    this.showAlert('success', 'Response approved and sent successfully');
                    await this.updatePendingResponses();
                    await this.updateStatus();
                } catch (error) {
                    this.showAlert('error', 'Failed to approve response');
                }
            }

            async rejectResponse(responseId) {
                try {
                    const result = await this.apiCall(`/pending-responses/${responseId}/reject`, 'POST');
                    this.showAlert('success', 'Response rejected successfully');
                    await this.updatePendingResponses();
                } catch (error) {
                    this.showAlert('error', 'Failed to reject response');
                }
            }

            startAutoRefresh() {
                this.refreshTimer = setInterval(() => {
                    this.updateStatus();
                    this.updateRecentEmails();
                    this.updatePendingResponses();
                }, this.refreshInterval);
            }

            stopAutoRefresh() {
                if (this.refreshTimer) {
                    clearInterval(this.refreshTimer);
                    this.refreshTimer = null;
                }
            }

            getPriorityClass(score) {
                if (score >= 7) return 'high';
                if (score >= 4) return 'medium';
                return 'low';
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }

        // Initialize dashboard when page loads
        let dashboard;
        document.addEventListener('DOMContentLoaded', () => {
            dashboard = new EmailAgentDashboard();
        });
    </script>
</body>
</html>
