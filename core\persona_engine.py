# core/persona_engine.py
# Personalization Engine for Email Agent

import re
from typing import Dict, <PERSON>, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
from config import AgentConfig

@dataclass
class EmailContext:
    """Container for email context and metadata"""
    sender: str
    subject: str
    body: str
    timestamp: datetime
    is_mytguy: bool = False
    thread_id: Optional[str] = None

class PersonaEngine:
    """Handles personalization logic for email processing"""
    
    def __init__(self):
        self.config = AgentConfig()
        self.mytguy_profile = self.config.MYTGUY_PROFILE
        self.user_prefs = self.config.USER_PREFERENCES
        
    def identify_sender_type(self, sender_email: str) -> str:
        """Identify if sender is MyTGuy or other contact type"""
        sender_lower = sender_email.lower()
        
        # Check if sender is MyTGuy
        for domain in self.mytguy_profile["email_domains"]:
            if domain.lower() in sender_lower:
                return "mytguy"
        
        # Could extend with other VIP categories
        return "standard"
    
    def extract_mytguy_signals(self, email_body: str) -> Dict[str, List[str]]:
        """Extract MyTGuy-specific communication signals"""
        signals = {
            "decision_requests": [],
            "urgency_indicators": [],
            "asks": [],
            "context_clues": []
        }
        
        body_lower = email_body.lower()
        
        # Decision indicators from MyTGuy profile
        for indicator in self.mytguy_profile["interaction_patterns"]["decision_indicators"]:
            if indicator in body_lower:
                signals["decision_requests"].append(indicator)
        
        # Urgency signals
        for signal in self.mytguy_profile["interaction_patterns"]["urgency_signals"]:
            if signal in body_lower:
                signals["urgency_indicators"].append(signal)
        
        # Extract questions and requests
        questions = re.findall(r'[.!?]\s*([^.!?]*\?)', email_body, re.IGNORECASE)
        signals["asks"] = [q.strip() for q in questions if len(q.strip()) > 5]
        
        return signals
    
    def calculate_priority_score(self, email_context: EmailContext) -> Tuple[int, List[str]]:
        """Calculate priority score (1-10) and reasoning"""
        score = 5  # Base score
        reasons = []
        
        # MyTGuy gets automatic priority boost
        if email_context.is_mytguy:
            score += 3
            reasons.append("Email from MyTGuy")
        
        # Check for priority keywords
        body_lower = email_context.body.lower()
        subject_lower = email_context.subject.lower()
        
        for keyword in self.user_prefs["priority_keywords"]:
            if keyword in body_lower or keyword in subject_lower:
                score += 2
                reasons.append(f"Contains priority keyword: '{keyword}'")
        
        # MyTGuy-specific urgency signals
        if email_context.is_mytguy:
            mytguy_signals = self.extract_mytguy_signals(email_context.body)
            if mytguy_signals["urgency_indicators"]:
                score += 2
                reasons.append("MyTGuy urgency signals detected")
            if mytguy_signals["decision_requests"]:
                score += 1
                reasons.append("Decision request from MyTGuy")
        
        # Time-based adjustments
        if email_context.timestamp:
            hour = email_context.timestamp.hour
            # Higher priority for emails during working hours
            if 9 <= hour <= 17:
                score += 1
                reasons.append("Received during working hours")
        
        return min(10, max(1, score)), reasons
    
    def get_summarization_style(self, sender_type: str) -> Dict[str, any]:
        """Get personalized summarization preferences based on sender"""
        base_style = {
            "length": self.user_prefs["summary_length"],
            "include_action_items": True,
            "tone": "professional"
        }
        
        if sender_type == "mytguy":
            # MyTGuy-specific adaptations
            base_style.update({
                "highlight_decisions": True,
                "extract_implicit_asks": True,
                "include_business_context": True,
                "format_preference": "bullet_points" if self.mytguy_profile["communication_style"]["prefers_bullet_points"] else "paragraphs"
            })
        
        return base_style
    
    def generate_personalized_insights(self, email_context: EmailContext) -> Dict[str, any]:
        """Generate insights based on sender relationship and content"""
        insights = {
            "sender_type": self.identify_sender_type(email_context.sender),
            "priority_score": 0,
            "priority_reasons": [],
            "recommended_actions": [],
            "mytguy_analysis": None
        }
        
        # Update email context with sender identification
        email_context.is_mytguy = insights["sender_type"] == "mytguy"
        
        # Calculate priority
        priority_score, reasons = self.calculate_priority_score(email_context)
        insights["priority_score"] = priority_score
        insights["priority_reasons"] = reasons
        
        # Generate recommendations
        insights["recommended_actions"] = self._generate_action_recommendations(email_context)
        
        # MyTGuy-specific analysis
        if email_context.is_mytguy:
            insights["mytguy_analysis"] = self.extract_mytguy_signals(email_context.body)
        
        return insights
    
    def _generate_action_recommendations(self, email_context: EmailContext) -> List[str]:
        """Generate contextual action recommendations"""
        actions = []
        
        if email_context.is_mytguy:
            mytguy_signals = self.extract_mytguy_signals(email_context.body)
            
            if mytguy_signals["decision_requests"]:
                actions.append("Prepare decision analysis/recommendation")
            
            if mytguy_signals["urgency_indicators"]:
                actions.append("Prioritize immediate response")
            
            if any(topic in email_context.body.lower() for topic in self.mytguy_profile["priority_topics"]):
                actions.append("Gather relevant project/business context")
        
        # General action detection
        if "meeting" in email_context.body.lower():
            actions.append("Check calendar availability")
        
        if any(word in email_context.body.lower() for word in ["deadline", "due", "by when"]):
            actions.append("Note deadline and add to task list")
        
        return actions