#!/usr/bin/env python3
"""
Smart Scheduling Module
Part of the MyTGuy Email Agent Architecture

This module provides intelligent calendar integration, meeting optimization,
and automated scheduling with MyTGuy-specific preferences and availability prediction.
"""

import re
import json
import logging
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import openai
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MeetingType(Enum):
    """Types of meetings that can be scheduled"""
    CLIENT_CALL = "client_call"
    INTERNAL_MEETING = "internal_meeting"
    PROJECT_REVIEW = "project_review"
    DISCOVERY_CALL = "discovery_call"
    STATUS_UPDATE = "status_update"
    DECISION_MEETING = "decision_meeting"
    TRAINING_SESSION = "training_session"
    VENDOR_MEETING = "vendor_meeting"

class MeetingPriority(Enum):
    """Priority levels for meetings"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5

class SchedulingPreference(Enum):
    """Scheduling preferences"""
    MORNING_PREFERRED = "morning_preferred"
    AFTERNOON_PREFERRED = "afternoon_preferred"
    AVOID_EARLY = "avoid_early"
    AVOID_LATE = "avoid_late"
    BACK_TO_BACK_OK = "back_to_back_ok"
    BUFFER_NEEDED = "buffer_needed"

@dataclass
class TimeSlot:
    """Represents a time slot for scheduling"""
    start_time: datetime
    end_time: datetime
    is_available: bool = True
    confidence: float = 1.0
    notes: str = ""

@dataclass
class MeetingRequest:
    """Represents a meeting request extracted from email"""
    id: str
    title: str
    description: str
    meeting_type: MeetingType
    priority: MeetingPriority
    duration_minutes: int
    participants: List[str]
    preferred_times: List[TimeSlot]
    deadline: Optional[datetime] = None
    location_preference: str = "virtual"
    source_email: Dict = None
    mytguy_context: Dict = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.source_email is None:
            self.source_email = {}
        if self.mytguy_context is None:
            self.mytguy_context = {}

@dataclass
class SchedulingSuggestion:
    """Represents a scheduling suggestion"""
    meeting_request_id: str
    suggested_time: TimeSlot
    confidence_score: float
    reasoning: List[str]
    alternative_times: List[TimeSlot]
    calendar_conflicts: List[Dict]
    preparation_time: int = 15  # minutes before meeting
    follow_up_time: int = 15   # minutes after meeting

class SmartScheduler:
    """Intelligent scheduling engine with MyTGuy preferences"""
    
    def __init__(self, openai_api_key: Optional[str] = None):
        self.openai_client = None
        if openai_api_key or os.getenv('OPENAI_API_KEY'):
            try:
                openai.api_key = openai_api_key or os.getenv('OPENAI_API_KEY')
                self.openai_client = openai
                logger.info("OpenAI client initialized for scheduling")
            except Exception as e:
                logger.warning(f"OpenAI initialization failed: {e}")
        
        # MyTGuy scheduling preferences
        self.mytguy_preferences = self._load_mytguy_preferences()
        self.meeting_patterns = self._load_meeting_patterns()
        self.scheduling_rules = self._load_scheduling_rules()
        
        # Mock calendar data (in production, would integrate with actual calendar API)
        self.calendar_events = []
        self.availability_cache = {}
    
    def _load_mytguy_preferences(self) -> Dict:
        """Load MyTGuy-specific scheduling preferences"""
        return {
            "working_hours": {
                "start": time(9, 0),   # 9:00 AM
                "end": time(17, 0),    # 5:00 PM
                "timezone": "EST"
            },
            "preferred_meeting_times": {
                "client_calls": [time(10, 0), time(14, 0)],  # 10 AM, 2 PM
                "internal_meetings": [time(9, 0), time(15, 0)],  # 9 AM, 3 PM
                "project_reviews": [time(11, 0), time(16, 0)]   # 11 AM, 4 PM
            },
            "meeting_duration_defaults": {
                MeetingType.CLIENT_CALL: 60,
                MeetingType.INTERNAL_MEETING: 30,
                MeetingType.PROJECT_REVIEW: 90,
                MeetingType.DISCOVERY_CALL: 45,
                MeetingType.STATUS_UPDATE: 30,
                MeetingType.DECISION_MEETING: 60,
                MeetingType.TRAINING_SESSION: 120,
                MeetingType.VENDOR_MEETING: 45
            },
            "scheduling_preferences": [
                SchedulingPreference.MORNING_PREFERRED,
                SchedulingPreference.BUFFER_NEEDED
            ],
            "avoid_times": {
                "lunch": (time(12, 0), time(13, 0)),
                "end_of_day": (time(16, 30), time(17, 0))
            },
            "buffer_time": {
                "before_meeting": 15,  # minutes
                "after_meeting": 15    # minutes
            }
        }
    
    def _load_meeting_patterns(self) -> Dict:
        """Load patterns for identifying meeting requests in emails"""
        return {
            "meeting_indicators": [
                r"(?:schedule|set up|arrange)\s+(?:a\s+)?(?:meeting|call|discussion)",
                r"(?:when are you|are you)\s+(?:available|free)",
                r"(?:let's|can we)\s+(?:meet|discuss|talk|schedule)",
                r"(?:calendar|availability|time)",
                r"(?:meeting|call|discussion)\s+(?:request|invitation)"
            ],
            "urgency_indicators": [
                r"(?:urgent|asap|immediately|critical)",
                r"(?:today|tomorrow|this week)",
                r"(?:need to meet|must discuss|important)"
            ],
            "duration_patterns": [
                r"(\d+)\s*(?:minute|min|hour|hr)s?\s+(?:meeting|call)",
                r"(?:quick|brief)\s+(?:meeting|call|chat)",
                r"(?:long|extended|detailed)\s+(?:meeting|discussion)"
            ],
            "participant_patterns": [
                r"(?:with|include|invite)\s+([^.!?]+)",
                r"(?:team|group|everyone|all)",
                r"(?:client|customer|vendor|partner)"
            ]
        }
    
    def _load_scheduling_rules(self) -> Dict:
        """Load intelligent scheduling rules"""
        return {
            "priority_rules": {
                "client_meetings": 4,  # High priority
                "internal_meetings": 2,  # Medium priority
                "vendor_meetings": 3   # Medium-high priority
            },
            "conflict_resolution": {
                "allow_overlap": False,
                "minimum_buffer": 15,  # minutes
                "maximum_daily_meetings": 6
            },
            "optimization_factors": {
                "travel_time": 0,  # Virtual meetings
                "preparation_time": True,
                "energy_levels": True,  # Consider time of day
                "meeting_clustering": True  # Group similar meetings
            }
        }
    
    def extract_meeting_requests_from_email(self, email_data: Dict) -> List[MeetingRequest]:
        """Extract meeting requests from email content"""
        try:
            sender = email_data.get('sender', '')
            subject = email_data.get('subject', '')
            body = email_data.get('body', '')
            
            meeting_requests = []
            
            # Check if email contains meeting request
            if self._contains_meeting_request(subject, body):
                # Extract meeting details
                meeting_type = self._determine_meeting_type(sender, subject, body)
                priority = self._determine_meeting_priority(subject, body)
                duration = self._extract_meeting_duration(body)
                participants = self._extract_participants(sender, body)
                preferred_times = self._extract_preferred_times(body)
                deadline = self._extract_scheduling_deadline(body)
                
                # Create meeting request
                request_id = f"meeting_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                meeting_request = MeetingRequest(
                    id=request_id,
                    title=self._generate_meeting_title(subject, meeting_type),
                    description=self._extract_meeting_description(body),
                    meeting_type=meeting_type,
                    priority=priority,
                    duration_minutes=duration,
                    participants=participants,
                    preferred_times=preferred_times,
                    deadline=deadline,
                    source_email=email_data,
                    mytguy_context=self._extract_mytguy_context(sender, subject, body)
                )
                
                meeting_requests.append(meeting_request)
            
            logger.info(f"Extracted {len(meeting_requests)} meeting requests from email")
            return meeting_requests
            
        except Exception as e:
            logger.error(f"Meeting request extraction failed: {e}")
            return []
    
    def suggest_optimal_times(self, meeting_request: MeetingRequest) -> SchedulingSuggestion:
        """Suggest optimal meeting times based on preferences and availability"""
        try:
            # Get available time slots
            available_slots = self._get_available_time_slots(
                meeting_request.duration_minutes,
                meeting_request.deadline
            )
            
            # Score and rank time slots
            scored_slots = self._score_time_slots(available_slots, meeting_request)
            
            # Select best option
            best_slot = scored_slots[0] if scored_slots else None
            alternatives = scored_slots[1:4] if len(scored_slots) > 1 else []
            
            # Generate reasoning
            reasoning = self._generate_scheduling_reasoning(best_slot, meeting_request)
            
            # Check for conflicts
            conflicts = self._check_calendar_conflicts(best_slot) if best_slot else []
            
            suggestion = SchedulingSuggestion(
                meeting_request_id=meeting_request.id,
                suggested_time=best_slot,
                confidence_score=best_slot.confidence if best_slot else 0.0,
                reasoning=reasoning,
                alternative_times=alternatives,
                calendar_conflicts=conflicts,
                preparation_time=self.mytguy_preferences["buffer_time"]["before_meeting"],
                follow_up_time=self.mytguy_preferences["buffer_time"]["after_meeting"]
            )
            
            logger.info(f"Generated scheduling suggestion with confidence: {suggestion.confidence_score}")
            return suggestion
            
        except Exception as e:
            logger.error(f"Scheduling suggestion failed: {e}")
            return None
    
    def _contains_meeting_request(self, subject: str, body: str) -> bool:
        """Check if email contains a meeting request"""
        content = f"{subject} {body}".lower()
        
        for pattern in self.meeting_patterns["meeting_indicators"]:
            if re.search(pattern, content, re.IGNORECASE):
                return True
        
        return False
    
    def _determine_meeting_type(self, sender: str, subject: str, body: str) -> MeetingType:
        """Determine the type of meeting based on content"""
        content = f"{subject} {body}".lower()
        
        # Check for specific meeting type indicators
        if any(word in content for word in ["client", "customer"]):
            return MeetingType.CLIENT_CALL
        elif any(word in content for word in ["review", "retrospective", "demo"]):
            return MeetingType.PROJECT_REVIEW
        elif any(word in content for word in ["discovery", "requirements", "scope"]):
            return MeetingType.DISCOVERY_CALL
        elif any(word in content for word in ["status", "update", "progress"]):
            return MeetingType.STATUS_UPDATE
        elif any(word in content for word in ["decision", "approve", "sign-off"]):
            return MeetingType.DECISION_MEETING
        elif any(word in content for word in ["training", "workshop", "learning"]):
            return MeetingType.TRAINING_SESSION
        elif any(word in content for word in ["vendor", "supplier", "partner"]):
            return MeetingType.VENDOR_MEETING
        elif "@mytguy.live" in sender:
            return MeetingType.INTERNAL_MEETING
        else:
            return MeetingType.CLIENT_CALL  # Default for external requests
    
    def _determine_meeting_priority(self, subject: str, body: str) -> MeetingPriority:
        """Determine meeting priority based on content"""
        content = f"{subject} {body}".lower()
        
        # Check for urgency indicators
        urgency_count = 0
        for pattern in self.meeting_patterns["urgency_indicators"]:
            if re.search(pattern, content, re.IGNORECASE):
                urgency_count += 1
        
        if urgency_count >= 2:
            return MeetingPriority.URGENT
        elif urgency_count >= 1:
            return MeetingPriority.HIGH
        elif any(word in content for word in ["important", "critical", "priority"]):
            return MeetingPriority.HIGH
        else:
            return MeetingPriority.MEDIUM
    
    def _extract_meeting_duration(self, body: str) -> int:
        """Extract meeting duration from email content"""
        # Look for explicit duration mentions
        for pattern in self.meeting_patterns["duration_patterns"]:
            match = re.search(pattern, body, re.IGNORECASE)
            if match:
                if "quick" in match.group(0).lower() or "brief" in match.group(0).lower():
                    return 30
                elif "long" in match.group(0).lower() or "extended" in match.group(0).lower():
                    return 90
                else:
                    # Try to extract number
                    duration_match = re.search(r'(\d+)', match.group(0))
                    if duration_match:
                        duration = int(duration_match.group(1))
                        if "hour" in match.group(0).lower():
                            return duration * 60
                        else:
                            return duration
        
        # Default duration based on content analysis
        content = body.lower()
        if any(word in content for word in ["quick", "brief", "short", "check-in"]):
            return 30
        elif any(word in content for word in ["detailed", "comprehensive", "workshop", "training"]):
            return 90
        else:
            return 60  # Default 1 hour
    
    def _extract_participants(self, sender: str, body: str) -> List[str]:
        """Extract meeting participants from email content"""
        participants = [sender]  # Always include sender
        
        # Look for participant mentions
        for pattern in self.meeting_patterns["participant_patterns"]:
            matches = re.finditer(pattern, body, re.IGNORECASE)
            for match in matches:
                participant_text = match.group(1) if match.groups() else match.group(0)
                # Simple participant extraction (would be more sophisticated in production)
                if "@" in participant_text:
                    email_matches = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', participant_text)
                    participants.extend(email_matches)
        
        # Remove duplicates and return
        return list(set(participants))
    
    def _extract_preferred_times(self, body: str) -> List[TimeSlot]:
        """Extract preferred meeting times from email content"""
        preferred_times = []
        
        # Look for time mentions
        time_patterns = [
            r'(\d{1,2}):(\d{2})\s*(AM|PM)',
            r'(\d{1,2})\s*(AM|PM)',
            r'(morning|afternoon|evening)',
            r'(today|tomorrow|this week|next week)',
            r'(\w+day)'  # Monday, Tuesday, etc.
        ]
        
        for pattern in time_patterns:
            matches = re.finditer(pattern, body, re.IGNORECASE)
            for match in matches:
                # Convert to TimeSlot (simplified implementation)
                time_slot = self._parse_time_mention(match.group(0))
                if time_slot:
                    preferred_times.append(time_slot)
        
        return preferred_times
    
    def _parse_time_mention(self, time_text: str) -> Optional[TimeSlot]:
        """Parse time mention into TimeSlot object"""
        try:
            time_text = time_text.lower().strip()
            now = datetime.now()
            
            # Handle relative time mentions
            if "morning" in time_text:
                start_time = now.replace(hour=10, minute=0, second=0, microsecond=0)
                end_time = start_time + timedelta(hours=1)
                return TimeSlot(start_time, end_time)
            elif "afternoon" in time_text:
                start_time = now.replace(hour=14, minute=0, second=0, microsecond=0)
                end_time = start_time + timedelta(hours=1)
                return TimeSlot(start_time, end_time)
            elif "today" in time_text:
                start_time = now.replace(hour=14, minute=0, second=0, microsecond=0)
                end_time = start_time + timedelta(hours=1)
                return TimeSlot(start_time, end_time)
            elif "tomorrow" in time_text:
                start_time = (now + timedelta(days=1)).replace(hour=14, minute=0, second=0, microsecond=0)
                end_time = start_time + timedelta(hours=1)
                return TimeSlot(start_time, end_time)
            
            # Handle specific time mentions (simplified)
            time_match = re.search(r'(\d{1,2}):?(\d{2})?\s*(AM|PM)', time_text, re.IGNORECASE)
            if time_match:
                hour = int(time_match.group(1))
                minute = int(time_match.group(2)) if time_match.group(2) else 0
                am_pm = time_match.group(3).upper()
                
                if am_pm == "PM" and hour != 12:
                    hour += 12
                elif am_pm == "AM" and hour == 12:
                    hour = 0
                
                start_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
                end_time = start_time + timedelta(hours=1)
                return TimeSlot(start_time, end_time)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to parse time mention: {time_text}, error: {e}")
            return None
    
    def _extract_scheduling_deadline(self, body: str) -> Optional[datetime]:
        """Extract deadline for scheduling the meeting"""
        deadline_patterns = [
            r"(?:by|before|deadline)\s+(\w+day)",
            r"(?:by|before|deadline)\s+(today|tomorrow|this week|next week)",
            r"(?:asap|immediately|urgent)"
        ]
        
        for pattern in deadline_patterns:
            match = re.search(pattern, body, re.IGNORECASE)
            if match:
                deadline_text = match.group(1) if match.groups() else match.group(0)
                return self._parse_deadline_text(deadline_text)
        
        return None
    
    def _parse_deadline_text(self, deadline_text: str) -> Optional[datetime]:
        """Parse deadline text into datetime"""
        try:
            deadline_text = deadline_text.lower().strip()
            now = datetime.now()
            
            if "today" in deadline_text or "asap" in deadline_text or "immediately" in deadline_text:
                return now + timedelta(hours=4)
            elif "tomorrow" in deadline_text:
                return (now + timedelta(days=1)).replace(hour=17, minute=0, second=0, microsecond=0)
            elif "this week" in deadline_text:
                days_until_friday = (4 - now.weekday()) % 7
                return (now + timedelta(days=days_until_friday)).replace(hour=17, minute=0, second=0, microsecond=0)
            elif "next week" in deadline_text:
                days_until_next_friday = (4 - now.weekday()) % 7 + 7
                return (now + timedelta(days=days_until_next_friday)).replace(hour=17, minute=0, second=0, microsecond=0)
            elif "friday" in deadline_text:
                days_until_friday = (4 - now.weekday()) % 7
                return (now + timedelta(days=days_until_friday)).replace(hour=17, minute=0, second=0, microsecond=0)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to parse deadline: {deadline_text}, error: {e}")
            return None
    
    def _extract_meeting_description(self, body: str) -> str:
        """Extract meeting description/agenda from email"""
        # Take first few sentences as description
        sentences = body.split('.')[:3]
        description = '. '.join(sentences).strip()
        
        if len(description) > 200:
            description = description[:200] + "..."
        
        return description
    
    def _extract_mytguy_context(self, sender: str, subject: str, body: str) -> Dict:
        """Extract MyTGuy-specific context for the meeting"""
        context = {
            "is_mytguy_internal": "@mytguy.live" in sender,
            "is_client_meeting": not ("@mytguy.live" in sender),
            "business_critical": False,
            "project_related": False
        }
        
        content = f"{subject} {body}".lower()
        
        # Check for business critical indicators
        if any(word in content for word in ["critical", "urgent", "important", "decision", "budget"]):
            context["business_critical"] = True
        
        # Check for project-related indicators
        if any(word in content for word in ["project", "milestone", "delivery", "implementation"]):
            context["project_related"] = True
        
        return context
    
    def _generate_meeting_title(self, subject: str, meeting_type: MeetingType) -> str:
        """Generate appropriate meeting title"""
        # Clean up subject
        clean_subject = re.sub(r'^(re:|fwd?:)\s*', '', subject, flags=re.IGNORECASE).strip()
        
        # Add meeting type prefix if not already present
        type_prefixes = {
            MeetingType.CLIENT_CALL: "Client Call:",
            MeetingType.INTERNAL_MEETING: "Team Meeting:",
            MeetingType.PROJECT_REVIEW: "Project Review:",
            MeetingType.DISCOVERY_CALL: "Discovery Call:",
            MeetingType.STATUS_UPDATE: "Status Update:",
            MeetingType.DECISION_MEETING: "Decision Meeting:",
            MeetingType.TRAINING_SESSION: "Training:",
            MeetingType.VENDOR_MEETING: "Vendor Meeting:"
        }
        
        prefix = type_prefixes.get(meeting_type, "Meeting:")
        
        if not any(indicator in clean_subject.lower() for indicator in ["call", "meeting", "discussion"]):
            return f"{prefix} {clean_subject}"
        else:
            return clean_subject
    
    def _get_available_time_slots(self, duration_minutes: int, deadline: Optional[datetime] = None) -> List[TimeSlot]:
        """Get available time slots for scheduling"""
        available_slots = []
        
        # Define search window
        start_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = deadline if deadline else start_date + timedelta(days=14)  # 2 weeks default
        
        # Generate time slots within working hours
        current_date = start_date
        while current_date <= end_date:
            # Skip weekends
            if current_date.weekday() >= 5:
                current_date += timedelta(days=1)
                continue
            
            # Generate slots for this day
            work_start = current_date.replace(
                hour=self.mytguy_preferences["working_hours"]["start"].hour,
                minute=self.mytguy_preferences["working_hours"]["start"].minute
            )
            work_end = current_date.replace(
                hour=self.mytguy_preferences["working_hours"]["end"].hour,
                minute=self.mytguy_preferences["working_hours"]["end"].minute
            )
            
            # Generate 30-minute slots
            current_time = work_start
            while current_time + timedelta(minutes=duration_minutes) <= work_end:
                slot_end = current_time + timedelta(minutes=duration_minutes)
                
                # Check if slot is available (simplified - would check actual calendar)
                if self._is_time_slot_available(current_time, slot_end):
                    available_slots.append(TimeSlot(current_time, slot_end))
                
                current_time += timedelta(minutes=30)  # 30-minute increments
            
            current_date += timedelta(days=1)
        
        return available_slots
    
    def _is_time_slot_available(self, start_time: datetime, end_time: datetime) -> bool:
        """Check if a time slot is available (mock implementation)"""
        # In production, this would check against actual calendar
        
        # Avoid lunch time
        lunch_start = start_time.replace(hour=12, minute=0)
        lunch_end = start_time.replace(hour=13, minute=0)
        
        if start_time < lunch_end and end_time > lunch_start:
            return False
        
        # Avoid end of day
        eod_start = start_time.replace(hour=16, minute=30)
        if start_time >= eod_start:
            return False
        
        # Mock some existing meetings
        existing_meetings = [
            (start_time.replace(hour=10, minute=0), start_time.replace(hour=11, minute=0)),
            (start_time.replace(hour=15, minute=0), start_time.replace(hour=16, minute=0))
        ]
        
        for existing_start, existing_end in existing_meetings:
            if start_time < existing_end and end_time > existing_start:
                return False
        
        return True
    
    def _score_time_slots(self, time_slots: List[TimeSlot], meeting_request: MeetingRequest) -> List[TimeSlot]:
        """Score and rank time slots based on preferences"""
        scored_slots = []
        
        for slot in time_slots:
            score = self._calculate_slot_score(slot, meeting_request)
            slot.confidence = score
            scored_slots.append(slot)
        
        # Sort by score (highest first)
        scored_slots.sort(key=lambda x: x.confidence, reverse=True)
        
        return scored_slots
    
    def _calculate_slot_score(self, slot: TimeSlot, meeting_request: MeetingRequest) -> float:
        """Calculate score for a time slot"""
        score = 0.5  # Base score
        
        # Time of day preferences
        hour = slot.start_time.hour
        
        # Morning preference for client calls
        if meeting_request.meeting_type == MeetingType.CLIENT_CALL:
            if 9 <= hour <= 11:
                score += 0.3
            elif 14 <= hour <= 16:
                score += 0.2
        
        # Internal meetings prefer different times
        elif meeting_request.meeting_type == MeetingType.INTERNAL_MEETING:
            if 9 <= hour <= 10 or 15 <= hour <= 16:
                score += 0.3
        
        # Priority boost
        if meeting_request.priority in [MeetingPriority.URGENT, MeetingPriority.CRITICAL]:
            if hour >= 9 and hour <= 16:  # During core hours
                score += 0.2
        
        # MyTGuy context boost
        if meeting_request.mytguy_context.get("business_critical"):
            score += 0.2
        
        # Avoid lunch time penalty
        if 12 <= hour <= 13:
            score -= 0.3
        
        # Avoid late day penalty
        if hour >= 16:
            score -= 0.2
        
        # Deadline urgency
        if meeting_request.deadline:
            days_until_deadline = (meeting_request.deadline - slot.start_time).days
            if days_until_deadline <= 1:
                score += 0.3
            elif days_until_deadline <= 3:
                score += 0.1
        
        # Ensure score is between 0 and 1
        return max(0.0, min(1.0, score))
    
    def _generate_scheduling_reasoning(self, time_slot: Optional[TimeSlot], meeting_request: MeetingRequest) -> List[str]:
        """Generate reasoning for scheduling suggestion"""
        if not time_slot:
            return ["No suitable time slots found within constraints"]
        
        reasoning = []
        
        hour = time_slot.start_time.hour
        
        # Time-based reasoning
        if 9 <= hour <= 11:
            reasoning.append("Morning time slot aligns with high productivity hours")
        elif 14 <= hour <= 16:
            reasoning.append("Afternoon slot provides good energy levels")
        
        # Meeting type reasoning
        if meeting_request.meeting_type == MeetingType.CLIENT_CALL:
            reasoning.append("Time slot optimized for client engagement")
        elif meeting_request.meeting_type == MeetingType.INTERNAL_MEETING:
            reasoning.append("Internal meeting scheduled during team availability")
        
        # Priority reasoning
        if meeting_request.priority in [MeetingPriority.URGENT, MeetingPriority.CRITICAL]:
            reasoning.append("High priority meeting scheduled promptly")
        
        # Buffer time reasoning
        reasoning.append(f"Includes {self.mytguy_preferences['buffer_time']['before_meeting']} min preparation time")
        
        return reasoning
    
    def _check_calendar_conflicts(self, time_slot: TimeSlot) -> List[Dict]:
        """Check for calendar conflicts (mock implementation)"""
        # In production, would check actual calendar
        conflicts = []
        
        # Mock conflict detection
        if time_slot.start_time.hour == 10:  # Mock existing meeting at 10 AM
            conflicts.append({
                "title": "Existing Team Standup",
                "start_time": time_slot.start_time,
                "end_time": time_slot.start_time + timedelta(minutes=30),
                "type": "conflict"
            })
        
        return conflicts

def main():
    """Demonstration of the smart scheduling system"""
    print("=== MyTGuy Email Agent - Smart Scheduling Demo ===\n")
    
    # Initialize scheduler
    scheduler = SmartScheduler()
    
    # Sample email with meeting request
    sample_email = {
        "sender": "<EMAIL>",
        "subject": "Schedule Project Review Meeting",
        "body": """Hi Jordan,
        
        I'd like to schedule a project review meeting to discuss our progress and next steps. 
        
        Could we meet sometime this week? I'm available in the mornings, preferably around 10 AM or 11 AM. 
        The meeting should take about 90 minutes as we have several topics to cover.
        
        Please let me know what works best for your schedule.
        
        Best regards,
        Client""",
        "timestamp": datetime.now()
    }
    
    # Extract meeting requests
    print("Extracting meeting requests from email...")
    meeting_requests = scheduler.extract_meeting_requests_from_email(sample_email)
    
    if meeting_requests:
        meeting_request = meeting_requests[0]
        
        print(f"\nExtracted meeting request:")
        print(f"Title: {meeting_request.title}")
        print(f"Type: {meeting_request.meeting_type.value}")
        print(f"Priority: {meeting_request.priority.name}")
        print(f"Duration: {meeting_request.duration_minutes} minutes")
        print(f"Participants: {', '.join(meeting_request.participants)}")
        
        # Generate scheduling suggestion
        print("\nGenerating optimal scheduling suggestion...")
        suggestion = scheduler.suggest_optimal_times(meeting_request)
        
        if suggestion and suggestion.suggested_time:
            print(f"\nScheduling Suggestion:")
            print(f"Recommended Time: {suggestion.suggested_time.start_time.strftime('%Y-%m-%d %H:%M')} - {suggestion.suggested_time.end_time.strftime('%H:%M')}")
            print(f"Confidence Score: {suggestion.confidence_score:.2f}")
            
            print(f"\nReasoning:")
            for reason in suggestion.reasoning:
                print(f"  • {reason}")
            
            if suggestion.alternative_times:
                print(f"\nAlternative Times:")
                for i, alt_time in enumerate(suggestion.alternative_times[:3], 1):
                    print(f"  {i}. {alt_time.start_time.strftime('%Y-%m-%d %H:%M')} - {alt_time.end_time.strftime('%H:%M')} (Score: {alt_time.confidence:.2f})")
            
            if suggestion.calendar_conflicts:
                print(f"\nCalendar Conflicts:")
                for conflict in suggestion.calendar_conflicts:
                    print(f"  • {conflict['title']}: {conflict['start_time'].strftime('%H:%M')} - {conflict['end_time'].strftime('%H:%M')}")
        else:
            print("No suitable time slots found.")
    else:
        print("No meeting requests found in the email.")

if __name__ == "__main__":
    main()

