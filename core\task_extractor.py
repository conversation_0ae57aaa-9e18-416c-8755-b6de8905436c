#!/usr/bin/env python3
"""
Proactive Task Extraction Module
Part of the MyTGuy Email Agent Architecture

This module extracts actionable tasks from emails, manages deadlines,
and provides intelligent follow-up scheduling with MyTGuy-specific prioritization.
"""

import re
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import openai
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5

class TaskStatus(Enum):
    """Task status types"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    OVERDUE = "overdue"
    CANCELLED = "cancelled"

class TaskType(Enum):
    """Types of tasks that can be extracted"""
    RESPONSE_REQUIRED = "response_required"
    MEETING_SCHEDULING = "meeting_scheduling"
    DOCUMENT_REVIEW = "document_review"
    DECISION_NEEDED = "decision_needed"
    FOLLOW_UP = "follow_up"
    DEADLINE_TASK = "deadline_task"
    APPROVAL_NEEDED = "approval_needed"
    INFORMATION_REQUEST = "information_request"

@dataclass
class ExtractedTask:
    """Represents a task extracted from an email"""
    id: str
    title: str
    description: str
    task_type: TaskType
    priority: TaskPriority
    status: TaskStatus
    due_date: Optional[datetime] = None
    estimated_duration: Optional[int] = None  # in minutes
    source_email: Dict = None
    mytguy_context: Dict = None
    action_required: str = ""
    assigned_to: str = "Jordan Lewis"
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
        if self.source_email is None:
            self.source_email = {}
        if self.mytguy_context is None:
            self.mytguy_context = {}

class TaskExtractor:
    """Core task extraction engine with MyTGuy intelligence"""
    
    def __init__(self, openai_api_key: Optional[str] = None):
        self.openai_client = None
        if openai_api_key or os.getenv('OPENAI_API_KEY'):
            try:
                openai.api_key = openai_api_key or os.getenv('OPENAI_API_KEY')
                self.openai_client = openai
                logger.info("OpenAI client initialized for task extraction")
            except Exception as e:
                logger.warning(f"OpenAI initialization failed: {e}")
        
        # Task extraction patterns
        self.task_patterns = self._load_task_patterns()
        self.deadline_patterns = self._load_deadline_patterns()
        self.mytguy_task_rules = self._load_mytguy_task_rules()
    
    def _load_task_patterns(self) -> Dict:
        """Load patterns for identifying different types of tasks"""
        return {
            "response_required": [
                r"(?:please|can you|could you|need you to)\s+(?:respond|reply|get back)",
                r"(?:let me know|tell me|inform me)",
                r"(?:your thoughts|your opinion|your feedback)",
                r"(?:what do you think|thoughts\?)"
            ],
            "meeting_scheduling": [
                r"(?:schedule|set up|arrange)\s+(?:a\s+)?(?:meeting|call|discussion)",
                r"(?:when are you|are you available)",
                r"(?:calendar|availability|free time)",
                r"(?:let's meet|let's discuss|let's talk)"
            ],
            "document_review": [
                r"(?:review|look at|check)\s+(?:the\s+)?(?:document|file|attachment)",
                r"(?:feedback on|comments on)\s+(?:the\s+)?(?:draft|proposal|document)",
                r"(?:please review|need you to review)"
            ],
            "decision_needed": [
                r"(?:need|require)\s+(?:a\s+)?(?:decision|approval)",
                r"(?:should we|do we|can we)",
                r"(?:what's your decision|make a decision)",
                r"(?:approve|sign off|green light)"
            ],
            "deadline_task": [
                r"(?:by|before|due)\s+(?:end of|EOD|close of business)",
                r"(?:deadline|due date|must be completed)",
                r"(?:urgent|asap|immediately|priority)"
            ],
            "information_request": [
                r"(?:need|require|looking for)\s+(?:information|details|data)",
                r"(?:can you provide|please send|share with me)",
                r"(?:status update|progress report|current state)"
            ]
        }
    
    def _load_deadline_patterns(self) -> List[str]:
        """Load patterns for extracting deadlines"""
        return [
            r"(?:by|before|due)\s+(\w+day)",  # by Friday, before Monday
            r"(?:by|before|due)\s+(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})",  # by 12/25/2024
            r"(?:by|before|due)\s+(end of (?:week|month|quarter))",
            r"(?:by|before|due)\s+(\d{1,2}:\d{2}\s*(?:AM|PM))",  # by 3:00 PM
            r"(?:deadline|due date):\s*([^\n\r]+)",
            r"(?:asap|immediately|urgent|priority)",
            r"(?:today|tomorrow|this week|next week|this month)"
        ]
    
    def _load_mytguy_task_rules(self) -> Dict:
        """Load MyTGuy-specific task prioritization rules"""
        return {
            "priority_multipliers": {
                "from_mytguy": 2.0,
                "mentions_mytguy": 1.5,
                "business_critical": 2.5,
                "client_related": 1.8
            },
            "mytguy_keywords": [
                "project", "client", "budget", "decision", "strategy",
                "implementation", "deadline", "meeting", "review"
            ],
            "urgency_indicators": [
                "urgent", "asap", "immediately", "critical", "priority",
                "need by", "deadline", "time sensitive"
            ],
            "business_context": [
                "revenue", "budget", "client", "contract", "proposal",
                "implementation", "delivery", "milestone"
            ]
        }
    
    def extract_tasks_from_email(self, email_data: Dict, mytguy_context: Dict = None) -> List[ExtractedTask]:
        """Extract all tasks from an email with MyTGuy intelligence"""
        try:
            sender = email_data.get('sender', '')
            subject = email_data.get('subject', '')
            body = email_data.get('body', '')
            timestamp = email_data.get('timestamp', datetime.now())
            
            # Determine if this is MyTGuy-related
            is_mytguy = self._is_mytguy_related(sender, subject, body)
            
            # Extract tasks using multiple methods
            rule_based_tasks = self._extract_tasks_rule_based(email_data, is_mytguy)
            
            if self.openai_client:
                ai_tasks = self._extract_tasks_ai_powered(email_data, is_mytguy)
                # Merge and deduplicate tasks
                all_tasks = self._merge_task_lists(rule_based_tasks, ai_tasks)
            else:
                all_tasks = rule_based_tasks
            
            # Apply MyTGuy-specific enhancements
            enhanced_tasks = self._apply_mytguy_enhancements(all_tasks, email_data, mytguy_context)
            
            # Set priorities and deadlines
            final_tasks = self._finalize_tasks(enhanced_tasks, email_data, is_mytguy)
            
            logger.info(f"Extracted {len(final_tasks)} tasks from email: {subject}")
            return final_tasks
            
        except Exception as e:
            logger.error(f"Task extraction failed: {e}")
            return []
    
    def _is_mytguy_related(self, sender: str, subject: str, body: str) -> bool:
        """Determine if email is MyTGuy-related"""
        mytguy_indicators = ["mytguy", "@mytguy.live"]
        content = f"{sender} {subject} {body}".lower()
        return any(indicator in content for indicator in mytguy_indicators)
    
    def _extract_tasks_rule_based(self, email_data: Dict, is_mytguy: bool) -> List[ExtractedTask]:
        """Extract tasks using rule-based pattern matching"""
        tasks = []
        body = email_data.get('body', '')
        subject = email_data.get('subject', '')
        
        # Check each task type pattern
        for task_type_name, patterns in self.task_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, body, re.IGNORECASE)
                for match in matches:
                    task = self._create_task_from_match(
                        match, task_type_name, email_data, is_mytguy
                    )
                    if task:
                        tasks.append(task)
        
        # Extract deadline-specific tasks
        deadline_tasks = self._extract_deadline_tasks(email_data, is_mytguy)
        tasks.extend(deadline_tasks)
        
        return tasks
    
    def _extract_tasks_ai_powered(self, email_data: Dict, is_mytguy: bool) -> List[ExtractedTask]:
        """Extract tasks using AI analysis"""
        try:
            prompt = self._build_task_extraction_prompt(email_data, is_mytguy)
            
            response = self.openai_client.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self._get_task_extraction_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1500,
                temperature=0.3
            )
            
            ai_response = response.choices[0].message.content
            tasks = self._parse_ai_task_response(ai_response, email_data, is_mytguy)
            
            return tasks
            
        except Exception as e:
            logger.error(f"AI task extraction failed: {e}")
            return []
    
    def _create_task_from_match(self, match, task_type_name: str, email_data: Dict, is_mytguy: bool) -> Optional[ExtractedTask]:
        """Create a task from a pattern match"""
        try:
            # Extract context around the match
            full_text = email_data.get('body', '')
            match_start = max(0, match.start() - 50)
            match_end = min(len(full_text), match.end() + 100)
            context = full_text[match_start:match_end].strip()
            
            # Generate task ID
            task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(context) % 1000}"
            
            # Determine task type
            task_type = TaskType(task_type_name)
            
            # Create basic task
            task = ExtractedTask(
                id=task_id,
                title=self._generate_task_title(context, task_type),
                description=context,
                task_type=task_type,
                priority=TaskPriority.MEDIUM,  # Will be adjusted later
                status=TaskStatus.PENDING,
                source_email=email_data,
                action_required=match.group(0)
            )
            
            return task
            
        except Exception as e:
            logger.error(f"Failed to create task from match: {e}")
            return None
    
    def _extract_deadline_tasks(self, email_data: Dict, is_mytguy: bool) -> List[ExtractedTask]:
        """Extract tasks with specific deadlines"""
        tasks = []
        body = email_data.get('body', '')
        
        for pattern in self.deadline_patterns:
            matches = re.finditer(pattern, body, re.IGNORECASE)
            for match in matches:
                deadline = self._parse_deadline(match.group(0))
                if deadline:
                    task_id = f"deadline_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(tasks)}"
                    
                    task = ExtractedTask(
                        id=task_id,
                        title=f"Deadline Task: {match.group(0)[:50]}",
                        description=f"Task with deadline: {match.group(0)}",
                        task_type=TaskType.DEADLINE_TASK,
                        priority=TaskPriority.HIGH,
                        status=TaskStatus.PENDING,
                        due_date=deadline,
                        source_email=email_data
                    )
                    tasks.append(task)
        
        return tasks
    
    def _parse_deadline(self, deadline_text: str) -> Optional[datetime]:
        """Parse deadline text into datetime object"""
        try:
            deadline_text = deadline_text.lower().strip()
            now = datetime.now()
            
            # Handle common deadline formats
            if "today" in deadline_text:
                return now.replace(hour=17, minute=0, second=0, microsecond=0)
            elif "tomorrow" in deadline_text:
                return (now + timedelta(days=1)).replace(hour=17, minute=0, second=0, microsecond=0)
            elif "this week" in deadline_text:
                days_until_friday = (4 - now.weekday()) % 7
                return (now + timedelta(days=days_until_friday)).replace(hour=17, minute=0, second=0, microsecond=0)
            elif "next week" in deadline_text:
                days_until_next_friday = (4 - now.weekday()) % 7 + 7
                return (now + timedelta(days=days_until_next_friday)).replace(hour=17, minute=0, second=0, microsecond=0)
            elif "end of week" in deadline_text:
                days_until_friday = (4 - now.weekday()) % 7
                return (now + timedelta(days=days_until_friday)).replace(hour=17, minute=0, second=0, microsecond=0)
            elif "asap" in deadline_text or "immediately" in deadline_text:
                return now + timedelta(hours=2)  # 2 hours from now
            
            # Try to parse specific dates (basic implementation)
            date_match = re.search(r'(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2,4})', deadline_text)
            if date_match:
                month, day, year = date_match.groups()
                if len(year) == 2:
                    year = f"20{year}"
                return datetime(int(year), int(month), int(day), 17, 0)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to parse deadline: {deadline_text}, error: {e}")
            return None
    
    def _apply_mytguy_enhancements(self, tasks: List[ExtractedTask], email_data: Dict, mytguy_context: Dict) -> List[ExtractedTask]:
        """Apply MyTGuy-specific enhancements to extracted tasks"""
        enhanced_tasks = []
        
        for task in tasks:
            # Add MyTGuy context
            if mytguy_context:
                task.mytguy_context = mytguy_context
            
            # Adjust priority based on MyTGuy rules
            task.priority = self._calculate_mytguy_priority(task, email_data)
            
            # Add MyTGuy-specific metadata
            task.mytguy_context.update({
                "is_mytguy_email": self._is_mytguy_related(
                    email_data.get('sender', ''),
                    email_data.get('subject', ''),
                    email_data.get('body', '')
                ),
                "business_critical": self._is_business_critical(task, email_data),
                "client_related": self._is_client_related(task, email_data)
            })
            
            enhanced_tasks.append(task)
        
        return enhanced_tasks
    
    def _calculate_mytguy_priority(self, task: ExtractedTask, email_data: Dict) -> TaskPriority:
        """Calculate priority with MyTGuy-specific rules"""
        base_priority = task.priority.value
        
        # MyTGuy email boost
        if self._is_mytguy_related(
            email_data.get('sender', ''),
            email_data.get('subject', ''),
            email_data.get('body', '')
        ):
            base_priority *= self.mytguy_task_rules["priority_multipliers"]["from_mytguy"]
        
        # Business critical boost
        if self._is_business_critical(task, email_data):
            base_priority *= self.mytguy_task_rules["priority_multipliers"]["business_critical"]
        
        # Client related boost
        if self._is_client_related(task, email_data):
            base_priority *= self.mytguy_task_rules["priority_multipliers"]["client_related"]
        
        # Urgency indicators
        content = f"{task.description} {email_data.get('body', '')}".lower()
        urgency_count = sum(1 for indicator in self.mytguy_task_rules["urgency_indicators"]
                           if indicator in content)
        base_priority += urgency_count * 0.5
        
        # Convert back to TaskPriority enum
        final_priority = min(5, max(1, int(base_priority)))
        return TaskPriority(final_priority)
    
    def _is_business_critical(self, task: ExtractedTask, email_data: Dict) -> bool:
        """Determine if task is business critical"""
        content = f"{task.description} {email_data.get('body', '')}".lower()
        return any(keyword in content for keyword in self.mytguy_task_rules["business_context"])
    
    def _is_client_related(self, task: ExtractedTask, email_data: Dict) -> bool:
        """Determine if task is client related"""
        content = f"{task.description} {email_data.get('body', '')}".lower()
        client_keywords = ["client", "customer", "proposal", "contract", "meeting"]
        return any(keyword in content for keyword in client_keywords)
    
    def _generate_task_title(self, context: str, task_type: TaskType) -> str:
        """Generate a concise task title"""
        # Clean up context
        context = re.sub(r'\s+', ' ', context).strip()
        
        # Generate title based on task type
        type_prefixes = {
            TaskType.RESPONSE_REQUIRED: "Respond to:",
            TaskType.MEETING_SCHEDULING: "Schedule:",
            TaskType.DOCUMENT_REVIEW: "Review:",
            TaskType.DECISION_NEEDED: "Decide on:",
            TaskType.FOLLOW_UP: "Follow up on:",
            TaskType.DEADLINE_TASK: "Complete by deadline:",
            TaskType.APPROVAL_NEEDED: "Approve:",
            TaskType.INFORMATION_REQUEST: "Provide info on:"
        }
        
        prefix = type_prefixes.get(task_type, "Task:")
        
        # Extract key phrases (simplified)
        key_phrase = context[:50] + "..." if len(context) > 50 else context
        
        return f"{prefix} {key_phrase}"
    
    def _finalize_tasks(self, tasks: List[ExtractedTask], email_data: Dict, is_mytguy: bool) -> List[ExtractedTask]:
        """Finalize tasks with proper metadata and scheduling"""
        finalized_tasks = []
        
        for task in tasks:
            # Set estimated duration based on task type
            task.estimated_duration = self._estimate_task_duration(task)
            
            # Set default due date if not already set
            if not task.due_date:
                task.due_date = self._suggest_due_date(task, is_mytguy)
            
            # Update timestamps
            task.updated_at = datetime.now()
            
            finalized_tasks.append(task)
        
        return finalized_tasks
    
    def _estimate_task_duration(self, task: ExtractedTask) -> int:
        """Estimate task duration in minutes"""
        duration_map = {
            TaskType.RESPONSE_REQUIRED: 15,
            TaskType.MEETING_SCHEDULING: 10,
            TaskType.DOCUMENT_REVIEW: 30,
            TaskType.DECISION_NEEDED: 20,
            TaskType.FOLLOW_UP: 10,
            TaskType.DEADLINE_TASK: 60,
            TaskType.APPROVAL_NEEDED: 15,
            TaskType.INFORMATION_REQUEST: 20
        }
        
        base_duration = duration_map.get(task.task_type, 30)
        
        # Adjust based on priority
        if task.priority in [TaskPriority.URGENT, TaskPriority.CRITICAL]:
            base_duration = int(base_duration * 0.8)  # Urgent tasks get done faster
        
        return base_duration
    
    def _suggest_due_date(self, task: ExtractedTask, is_mytguy: bool) -> datetime:
        """Suggest appropriate due date for task"""
        now = datetime.now()
        
        # MyTGuy tasks get tighter deadlines
        if is_mytguy:
            if task.priority in [TaskPriority.URGENT, TaskPriority.CRITICAL]:
                return now + timedelta(hours=4)
            elif task.priority == TaskPriority.HIGH:
                return now + timedelta(hours=24)
            else:
                return now + timedelta(days=2)
        else:
            if task.priority in [TaskPriority.URGENT, TaskPriority.CRITICAL]:
                return now + timedelta(hours=8)
            elif task.priority == TaskPriority.HIGH:
                return now + timedelta(days=2)
            else:
                return now + timedelta(days=5)
    
    def _merge_task_lists(self, rule_tasks: List[ExtractedTask], ai_tasks: List[ExtractedTask]) -> List[ExtractedTask]:
        """Merge and deduplicate task lists from different extraction methods"""
        # Simple deduplication based on similar descriptions
        merged_tasks = rule_tasks.copy()
        
        for ai_task in ai_tasks:
            is_duplicate = False
            for existing_task in merged_tasks:
                # Check for similarity (simplified)
                if self._tasks_are_similar(ai_task, existing_task):
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                merged_tasks.append(ai_task)
        
        return merged_tasks
    
    def _tasks_are_similar(self, task1: ExtractedTask, task2: ExtractedTask) -> bool:
        """Check if two tasks are similar enough to be considered duplicates"""
        # Simple similarity check based on description overlap
        desc1_words = set(task1.description.lower().split())
        desc2_words = set(task2.description.lower().split())
        
        if len(desc1_words) == 0 or len(desc2_words) == 0:
            return False
        
        overlap = len(desc1_words.intersection(desc2_words))
        total_words = len(desc1_words.union(desc2_words))
        
        similarity = overlap / total_words if total_words > 0 else 0
        return similarity > 0.6  # 60% similarity threshold
    
    def _build_task_extraction_prompt(self, email_data: Dict, is_mytguy: bool) -> str:
        """Build prompt for AI task extraction"""
        mytguy_context = "This email is from MyTGuy domain - prioritize business-critical tasks." if is_mytguy else ""
        
        return f"""
        Extract actionable tasks from this email for Jordan Lewis:
        
        From: {email_data.get('sender', '')}
        Subject: {email_data.get('subject', '')}
        
        {mytguy_context}
        
        Email Body:
        {email_data.get('body', '')}
        
        For each task, identify:
        1. Task type (response_required, meeting_scheduling, document_review, decision_needed, etc.)
        2. Priority level (1-5)
        3. Due date/deadline if mentioned
        4. Specific action required
        5. Estimated time needed
        
        Return tasks in JSON format with clear, actionable titles.
        """
    
    def _get_task_extraction_system_prompt(self) -> str:
        """Get system prompt for AI task extraction"""
        return """
        You are an expert task extraction assistant for Jordan Lewis, owner of MyTGuy.
        
        Extract specific, actionable tasks from emails. Focus on:
        - Clear action items that require Jordan's attention
        - Deadlines and time-sensitive requests
        - Decision points and approval needs
        - Follow-up requirements
        - Meeting scheduling needs
        
        Prioritize MyTGuy business-related tasks higher.
        Be specific and actionable in task descriptions.
        """
    
    def _parse_ai_task_response(self, ai_response: str, email_data: Dict, is_mytguy: bool) -> List[ExtractedTask]:
        """Parse AI response into ExtractedTask objects"""
        tasks = []
        
        try:
            # Try to parse as JSON first
            if ai_response.strip().startswith('[') or ai_response.strip().startswith('{'):
                task_data = json.loads(ai_response)
                if isinstance(task_data, dict):
                    task_data = [task_data]
                
                for item in task_data:
                    task = self._create_task_from_ai_data(item, email_data, is_mytguy)
                    if task:
                        tasks.append(task)
            else:
                # Parse as text format
                tasks = self._parse_text_task_response(ai_response, email_data, is_mytguy)
                
        except Exception as e:
            logger.error(f"Failed to parse AI task response: {e}")
            # Fallback to text parsing
            tasks = self._parse_text_task_response(ai_response, email_data, is_mytguy)
        
        return tasks
    
    def _create_task_from_ai_data(self, task_data: Dict, email_data: Dict, is_mytguy: bool) -> Optional[ExtractedTask]:
        """Create ExtractedTask from AI-parsed data"""
        try:
            task_id = f"ai_task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(str(task_data)) % 1000}"
            
            # Parse task type
            task_type_str = task_data.get('task_type', 'response_required')
            try:
                task_type = TaskType(task_type_str)
            except ValueError:
                task_type = TaskType.RESPONSE_REQUIRED
            
            # Parse priority
            priority_val = task_data.get('priority', 3)
            try:
                priority = TaskPriority(int(priority_val))
            except (ValueError, TypeError):
                priority = TaskPriority.MEDIUM
            
            # Parse due date
            due_date = None
            if task_data.get('due_date'):
                due_date = self._parse_deadline(task_data['due_date'])
            
            task = ExtractedTask(
                id=task_id,
                title=task_data.get('title', 'AI Extracted Task'),
                description=task_data.get('description', ''),
                task_type=task_type,
                priority=priority,
                status=TaskStatus.PENDING,
                due_date=due_date,
                estimated_duration=task_data.get('estimated_duration', 30),
                source_email=email_data,
                action_required=task_data.get('action_required', '')
            )
            
            return task
            
        except Exception as e:
            logger.error(f"Failed to create task from AI data: {e}")
            return None
    
    def _parse_text_task_response(self, text_response: str, email_data: Dict, is_mytguy: bool) -> List[ExtractedTask]:
        """Parse text-format AI response into tasks"""
        tasks = []
        
        # Split by lines and look for task indicators
        lines = text_response.split('\n')
        current_task = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Look for task indicators
            if any(indicator in line.lower() for indicator in ['task:', 'action:', '•', '-', '1.', '2.', '3.']):
                if current_task:
                    tasks.append(current_task)
                
                # Create new task
                task_id = f"text_task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(tasks)}"
                current_task = ExtractedTask(
                    id=task_id,
                    title=line[:100],
                    description=line,
                    task_type=TaskType.RESPONSE_REQUIRED,
                    priority=TaskPriority.MEDIUM,
                    status=TaskStatus.PENDING,
                    source_email=email_data
                )
            elif current_task:
                # Add to current task description
                current_task.description += f" {line}"
        
        # Add the last task
        if current_task:
            tasks.append(current_task)
        
        return tasks

def main():
    """Demonstration of the task extraction system"""
    print("=== MyTGuy Email Agent - Task Extraction Demo ===\n")
    
    # Initialize task extractor
    extractor = TaskExtractor()
    
    # Sample email for testing
    sample_email = {
        "sender": "<EMAIL>",
        "subject": "Urgent: Q4 Planning and Client Meeting Prep",
        "body": """Hi Jordan,
        
        I need your thoughts on several urgent items before our client meeting tomorrow:
        
        1. Please review the Q4 budget proposal by end of day today
        2. Can you schedule a follow-up meeting with the Johnson client for next week?
        3. We need to make a decision on the new vendor contracts by Friday
        4. Please provide feedback on the marketing strategy document I sent earlier
        
        Also, the client is expecting our implementation timeline by tomorrow at 2 PM.
        This is critical for securing the contract.
        
        Let me know your thoughts ASAP.
        
        Thanks,
        MyTGuy""",
        "timestamp": datetime.now()
    }
    
    # Extract tasks
    print("Extracting tasks from sample email...")
    tasks = extractor.extract_tasks_from_email(sample_email)
    
    # Display results
    print(f"\nExtracted {len(tasks)} tasks:\n")
    
    for i, task in enumerate(tasks, 1):
        print(f"--- Task {i} ---")
        print(f"Title: {task.title}")
        print(f"Type: {task.task_type.value}")
        print(f"Priority: {task.priority.name}")
        print(f"Due Date: {task.due_date.strftime('%Y-%m-%d %H:%M') if task.due_date else 'Not specified'}")
        print(f"Estimated Duration: {task.estimated_duration} minutes")
        print(f"Action Required: {task.action_required}")
        print(f"Description: {task.description[:100]}...")
        if task.mytguy_context:
            print(f"MyTGuy Context: {task.mytguy_context}")
        print()

if __name__ == "__main__":
    main()

