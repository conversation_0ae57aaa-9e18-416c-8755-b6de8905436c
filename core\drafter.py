#!/usr/bin/env python3
"""
Intelligent Email Drafting Module
Part of the MyTGuy Email Agent Architecture

This module provides AI-powered email draft generation with MyTGuy-specific
personalization, tone adaptation, and context-aware content optimization.
"""

import re
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import openai
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DraftType(Enum):
    """Types of email drafts that can be generated"""
    REPLY = "reply"
    FORWARD = "forward"
    NEW_EMAIL = "new_email"
    FOLLOW_UP = "follow_up"
    MEETING_REQUEST = "meeting_request"
    STATUS_UPDATE = "status_update"

class ToneStyle(Enum):
    """Available tone styles for email drafts"""
    PROFESSIONAL = "professional"
    FRIENDLY = "friendly"
    CASUAL = "casual"
    FORMAL = "formal"
    URGENT = "urgent"
    APOLOGETIC = "apologetic"
    ENTHUSIASTIC = "enthusiastic"

@dataclass
class DraftContext:
    """Context information for draft generation"""
    original_email: Optional[Dict] = None
    recipient_info: Dict = None
    mytguy_relationship: str = "unknown"  # client, prospect, partner, internal
    communication_history: List[Dict] = None
    project_context: str = ""
    urgency_level: int = 1  # 1-5 scale
    required_actions: List[str] = None
    meeting_info: Dict = None
    
    def __post_init__(self):
        if self.recipient_info is None:
            self.recipient_info = {}
        if self.communication_history is None:
            self.communication_history = []
        if self.required_actions is None:
            self.required_actions = []

@dataclass
class EmailDraft:
    """Generated email draft with metadata"""
    subject: str
    body: str
    tone_style: ToneStyle
    confidence_score: float
    mytguy_insights: Dict
    suggested_send_time: Optional[datetime] = None
    attachments_needed: List[str] = None
    follow_up_required: bool = False
    priority_level: int = 1
    
    def __post_init__(self):
        if self.attachments_needed is None:
            self.attachments_needed = []

class MyTGuyDraftingEngine:
    """Core drafting engine with MyTGuy intelligence"""
    
    def __init__(self, openai_api_key: Optional[str] = None):
        self.openai_client = None
        if openai_api_key or os.getenv('OPENAI_API_KEY'):
            try:
                openai.api_key = openai_api_key or os.getenv('OPENAI_API_KEY')
                self.openai_client = openai
                logger.info("OpenAI client initialized")
            except Exception as e:
                logger.warning(f"OpenAI initialization failed: {e}")
        
        # MyTGuy-specific knowledge base
        self.mytguy_templates = self._load_mytguy_templates()
        self.relationship_patterns = self._load_relationship_patterns()
        self.industry_context = self._load_industry_context()
    
    def _load_mytguy_templates(self) -> Dict:
        """Load MyTGuy-specific email templates"""
        return {
            "client_onboarding": {
                "subject_patterns": [
                    "Welcome to MyTGuy - Let's Get Started!",
                    "Your MyTGuy Journey Begins Here",
                    "Next Steps for Your MyTGuy Implementation"
                ],
                "opening_lines": [
                    "Thank you for choosing MyTGuy as your technology partner.",
                    "We're excited to begin this journey with you.",
                    "Welcome to the MyTGuy family!"
                ],
                "closing_lines": [
                    "Looking forward to delivering exceptional results together.",
                    "We're here to ensure your success every step of the way.",
                    "Let's make great things happen together."
                ]
            },
            "project_updates": {
                "subject_patterns": [
                    "MyTGuy Project Update - [PROJECT_NAME]",
                    "Weekly Progress Report - [PROJECT_NAME]",
                    "[PROJECT_NAME] Status & Next Steps"
                ],
                "status_phrases": [
                    "We're making excellent progress on",
                    "This week we've achieved",
                    "Moving forward with strong momentum on"
                ]
            },
            "technical_discussions": {
                "subject_patterns": [
                    "Technical Discussion: [TOPIC]",
                    "MyTGuy Solution Architecture - [TOPIC]",
                    "Implementation Strategy for [TOPIC]"
                ],
                "technical_openers": [
                    "Based on our technical analysis,",
                    "From a MyTGuy perspective,",
                    "Our recommended approach involves"
                ]
            }
        }
    
    def _load_relationship_patterns(self) -> Dict:
        """Load relationship-specific communication patterns"""
        return {
            "client": {
                "tone": ToneStyle.PROFESSIONAL,
                "formality": 0.8,
                "detail_level": "high",
                "follow_up_frequency": "regular"
            },
            "prospect": {
                "tone": ToneStyle.ENTHUSIASTIC,
                "formality": 0.7,
                "detail_level": "medium",
                "follow_up_frequency": "strategic"
            },
            "partner": {
                "tone": ToneStyle.FRIENDLY,
                "formality": 0.6,
                "detail_level": "medium",
                "follow_up_frequency": "collaborative"
            },
            "internal": {
                "tone": ToneStyle.CASUAL,
                "formality": 0.4,
                "detail_level": "high",
                "follow_up_frequency": "as_needed"
            }
        }
    
    def _load_industry_context(self) -> Dict:
        """Load industry-specific context and terminology"""
        return {
            "technology": {
                "keywords": ["implementation", "architecture", "scalability", "integration"],
                "pain_points": ["technical debt", "system integration", "performance optimization"],
                "solutions": ["custom development", "API integration", "cloud migration"]
            },
            "consulting": {
                "keywords": ["strategy", "optimization", "transformation", "efficiency"],
                "pain_points": ["process inefficiencies", "resource allocation", "growth challenges"],
                "solutions": ["strategic planning", "process improvement", "technology enablement"]
            }
        }
    
    def generate_draft(self, draft_type: DraftType, context: DraftContext) -> EmailDraft:
        """Generate an intelligent email draft with MyTGuy personalization"""
        try:
            # Analyze context and determine optimal approach
            analysis = self._analyze_drafting_context(context)
            
            # Generate draft using AI or rule-based approach
            if self.openai_client and analysis["complexity"] > 0.6:
                draft = self._generate_ai_draft(draft_type, context, analysis)
            else:
                draft = self._generate_rule_based_draft(draft_type, context, analysis)
            
            # Apply MyTGuy-specific enhancements
            enhanced_draft = self._apply_mytguy_enhancements(draft, context, analysis)
            
            # Optimize for recipient and relationship
            optimized_draft = self._optimize_for_relationship(enhanced_draft, context)
            
            logger.info(f"Generated {draft_type.value} draft with confidence: {optimized_draft.confidence_score}")
            return optimized_draft
            
        except Exception as e:
            logger.error(f"Draft generation failed: {e}")
            return self._generate_fallback_draft(draft_type, context)
    
    def _analyze_drafting_context(self, context: DraftContext) -> Dict:
        """Analyze context to determine drafting strategy"""
        analysis = {
            "complexity": 0.0,
            "mytguy_relevance": 0.0,
            "urgency_factor": context.urgency_level / 5.0,
            "relationship_strength": 0.0,
            "technical_content": False,
            "business_critical": False
        }
        
        # Analyze MyTGuy domain relevance
        mytguy_indicators = ["mytguy", "implementation", "project", "technical", "development"]
        if context.original_email:
            content = f"{context.original_email.get('subject', '')} {context.original_email.get('body', '')}"
            mytguy_mentions = sum(1 for indicator in mytguy_indicators if indicator.lower() in content.lower())
            analysis["mytguy_relevance"] = min(mytguy_mentions / 3.0, 1.0)
        
        # Assess relationship strength
        relationship_scores = {"client": 0.9, "partner": 0.8, "prospect": 0.6, "internal": 0.7}
        analysis["relationship_strength"] = relationship_scores.get(context.mytguy_relationship, 0.5)
        
        # Determine complexity
        complexity_factors = [
            len(context.required_actions) * 0.1,
            len(context.communication_history) * 0.05,
            analysis["mytguy_relevance"] * 0.3,
            analysis["urgency_factor"] * 0.2
        ]
        analysis["complexity"] = min(sum(complexity_factors), 1.0)
        
        # Check for technical content
        technical_keywords = ["api", "database", "server", "code", "development", "architecture"]
        if context.project_context:
            analysis["technical_content"] = any(keyword in context.project_context.lower() 
                                               for keyword in technical_keywords)
        
        return analysis
    
    def _generate_ai_draft(self, draft_type: DraftType, context: DraftContext, analysis: Dict) -> EmailDraft:
        """Generate draft using OpenAI with MyTGuy context"""
        try:
            prompt = self._build_ai_prompt(draft_type, context, analysis)
            
            response = self.openai_client.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.7
            )
            
            draft_content = response.choices[0].message.content
            parsed_draft = self._parse_ai_response(draft_content, context, analysis)
            
            return parsed_draft
            
        except Exception as e:
            logger.error(f"AI draft generation failed: {e}")
            return self._generate_rule_based_draft(draft_type, context, analysis)
    
    def _generate_rule_based_draft(self, draft_type: DraftType, context: DraftContext, analysis: Dict) -> EmailDraft:
        """Generate draft using rule-based approach with MyTGuy templates"""
        
        # Select appropriate template
        template_key = self._select_template(draft_type, context, analysis)
        template = self.mytguy_templates.get(template_key, {})
        
        # Generate subject line
        subject = self._generate_subject(draft_type, context, template)
        
        # Generate email body
        body = self._generate_body(draft_type, context, template, analysis)
        
        # Determine tone and confidence
        tone = self._determine_tone(context, analysis)
        confidence = self._calculate_confidence(analysis, "rule_based")
        
        # Extract MyTGuy insights
        insights = self._extract_mytguy_insights(context, analysis)
        
        return EmailDraft(
            subject=subject,
            body=body,
            tone_style=tone,
            confidence_score=confidence,
            mytguy_insights=insights,
            suggested_send_time=self._suggest_send_time(context, analysis),
            follow_up_required=self._needs_follow_up(context, analysis),
            priority_level=max(1, int(analysis["urgency_factor"] * 5))
        )
    
    def _select_template(self, draft_type: DraftType, context: DraftContext, analysis: Dict) -> str:
        """Select most appropriate MyTGuy template"""
        if context.mytguy_relationship == "client" and "onboard" in context.project_context.lower():
            return "client_onboarding"
        elif "project" in context.project_context.lower() or context.required_actions:
            return "project_updates"
        elif analysis["technical_content"]:
            return "technical_discussions"
        else:
            return "client_onboarding"  # Default template
    
    def _generate_subject(self, draft_type: DraftType, context: DraftContext, template: Dict) -> str:
        """Generate contextually appropriate subject line"""
        if context.original_email and draft_type == DraftType.REPLY:
            original_subject = context.original_email.get('subject', '')
            if not original_subject.lower().startswith('re:'):
                return f"Re: {original_subject}"
            return original_subject
        
        # Use template patterns
        patterns = template.get('subject_patterns', ['MyTGuy Update'])
        base_subject = patterns[0]  # Use first pattern as default
        
        # Replace placeholders
        if '[PROJECT_NAME]' in base_subject and context.project_context:
            project_name = context.project_context.split()[0].title()
            base_subject = base_subject.replace('[PROJECT_NAME]', project_name)
        
        # Add urgency indicators if needed
        if context.urgency_level >= 4:
            base_subject = f"URGENT: {base_subject}"
        elif context.urgency_level >= 3:
            base_subject = f"Priority: {base_subject}"
        
        return base_subject
    
    def _generate_body(self, draft_type: DraftType, context: DraftContext, template: Dict, analysis: Dict) -> str:
        """Generate email body with MyTGuy personalization"""
        body_parts = []
        
        # Opening
        opening = self._generate_opening(context, template, analysis)
        body_parts.append(opening)
        
        # Main content
        main_content = self._generate_main_content(draft_type, context, analysis)
        body_parts.append(main_content)
        
        # Action items if present
        if context.required_actions:
            action_section = self._generate_action_section(context.required_actions)
            body_parts.append(action_section)
        
        # MyTGuy-specific insights
        if analysis["mytguy_relevance"] > 0.5:
            insights_section = self._generate_insights_section(context, analysis)
            body_parts.append(insights_section)
        
        # Closing
        closing = self._generate_closing(context, template, analysis)
        body_parts.append(closing)
        
        # Signature
        signature = self._generate_signature(context)
        body_parts.append(signature)
        
        return "\n\n".join(filter(None, body_parts))
    
    def _generate_opening(self, context: DraftContext, template: Dict, analysis: Dict) -> str:
        """Generate personalized opening based on relationship and context"""
        recipient_name = context.recipient_info.get('name', 'there')
        
        # Relationship-based greetings
        relationship_greetings = {
            "client": f"Hi {recipient_name},",
            "prospect": f"Hello {recipient_name},",
            "partner": f"Hi {recipient_name},",
            "internal": f"Hey {recipient_name},"
        }
        
        greeting = relationship_greetings.get(context.mytguy_relationship, f"Hello {recipient_name},")
        
        # Add context-specific opening line
        opening_lines = template.get('opening_lines', ["Thank you for your message."])
        if context.original_email and "thank" in context.original_email.get('body', '').lower():
            opening_line = "Thank you for reaching out."
        else:
            opening_line = opening_lines[0]
        
        return f"{greeting}\n\n{opening_line}"
    
    def _generate_main_content(self, draft_type: DraftType, context: DraftContext, analysis: Dict) -> str:
        """Generate main email content based on type and context"""
        if draft_type == DraftType.REPLY and context.original_email:
            return self._generate_reply_content(context, analysis)
        elif draft_type == DraftType.FOLLOW_UP:
            return self._generate_followup_content(context, analysis)
        elif draft_type == DraftType.STATUS_UPDATE:
            return self._generate_status_content(context, analysis)
        else:
            return self._generate_general_content(context, analysis)
    
    def _generate_reply_content(self, context: DraftContext, analysis: Dict) -> str:
        """Generate reply-specific content"""
        original_body = context.original_email.get('body', '')
        
        # Acknowledge key points from original email
        acknowledgment = "Thank you for your message."
        
        # Address specific questions or requests
        if "?" in original_body:
            response = "I'll be happy to address your questions."
        elif any(word in original_body.lower() for word in ['help', 'support', 'issue', 'problem']):
            response = "I understand you need assistance, and I'm here to help."
        else:
            response = "I appreciate you keeping me updated."
        
        # MyTGuy-specific context
        mytguy_context = ""
        if analysis["mytguy_relevance"] > 0.6:
            mytguy_context = "From a MyTGuy perspective, this aligns well with our implementation strategy."
        
        return f"{acknowledgment} {response}\n\n{mytguy_context}".strip()
    
    def _generate_followup_content(self, context: DraftContext, analysis: Dict) -> str:
        """Generate follow-up specific content"""
        base_content = "I wanted to follow up on our recent discussion"
        
        if context.project_context:
            base_content += f" regarding {context.project_context.lower()}"
        
        base_content += "."
        
        # Add MyTGuy value proposition if relevant
        if analysis["mytguy_relevance"] > 0.5:
            value_add = "\n\nAs we move forward with the MyTGuy implementation, I believe this will significantly enhance your operational efficiency."
            base_content += value_add
        
        return base_content
    
    def _generate_status_content(self, context: DraftContext, analysis: Dict) -> str:
        """Generate status update content"""
        status_phrases = self.mytguy_templates["project_updates"]["status_phrases"]
        base_phrase = status_phrases[0]
        
        content = f"{base_phrase} {context.project_context or 'the current project'}."
        
        # Add progress indicators
        if context.required_actions:
            content += f"\n\nKey accomplishments this period:\n"
            for i, action in enumerate(context.required_actions[:3], 1):
                content += f"{i}. {action}\n"
        
        return content
    
    def _generate_general_content(self, context: DraftContext, analysis: Dict) -> str:
        """Generate general content for other email types"""
        if analysis["mytguy_relevance"] > 0.6:
            return "I'm reaching out regarding our MyTGuy collaboration and wanted to share some important updates."
        else:
            return "I hope this message finds you well. I wanted to touch base with you on a few important matters."
    
    def _generate_action_section(self, actions: List[str]) -> str:
        """Generate action items section"""
        if not actions:
            return ""
        
        section = "Action Items:\n"
        for i, action in enumerate(actions, 1):
            section += f"{i}. {action}\n"
        
        return section.rstrip()
    
    def _generate_insights_section(self, context: DraftContext, analysis: Dict) -> str:
        """Generate MyTGuy-specific insights section"""
        insights = []
        
        if analysis["technical_content"]:
            insights.append("Technical Implementation: Our MyTGuy approach ensures scalable and maintainable solutions.")
        
        if context.urgency_level >= 3:
            insights.append("Priority Focus: This aligns with our commitment to delivering timely, high-quality results.")
        
        if context.mytguy_relationship == "client":
            insights.append("Client Success: We're dedicated to ensuring your MyTGuy investment delivers maximum value.")
        
        if not insights:
            return ""
        
        return "MyTGuy Insights:\n" + "\n".join(f"• {insight}" for insight in insights)
    
    def _generate_closing(self, context: DraftContext, template: Dict, analysis: Dict) -> str:
        """Generate appropriate closing"""
        closing_lines = template.get('closing_lines', ["Thank you for your time."])
        base_closing = closing_lines[0]
        
        # Add availability statement
        availability = ""
        if context.urgency_level >= 3:
            availability = "Please don't hesitate to reach out if you need immediate assistance."
        else:
            availability = "Feel free to reach out if you have any questions."
        
        return f"{base_closing}\n\n{availability}"
    
    def _generate_signature(self, context: DraftContext) -> str:
        """Generate professional signature"""
        return """Best regards,
[Your Name]
MyTGuy Team
[Your Contact Information]"""
    
    def _determine_tone(self, context: DraftContext, analysis: Dict) -> ToneStyle:
        """Determine appropriate tone based on context"""
        relationship_tones = self.relationship_patterns.get(context.mytguy_relationship, {})
        base_tone = relationship_tones.get("tone", ToneStyle.PROFESSIONAL)
        
        # Adjust for urgency
        if context.urgency_level >= 4:
            return ToneStyle.URGENT
        elif context.urgency_level >= 3:
            return ToneStyle.PROFESSIONAL
        
        return base_tone
    
    def _calculate_confidence(self, analysis: Dict, method: str) -> float:
        """Calculate confidence score for the generated draft"""
        base_confidence = 0.7 if method == "ai" else 0.6
        
        # Boost confidence based on MyTGuy relevance
        mytguy_boost = analysis["mytguy_relevance"] * 0.2
        
        # Adjust for relationship strength
        relationship_boost = analysis["relationship_strength"] * 0.1
        
        # Factor in complexity handling
        complexity_factor = 1.0 - (analysis["complexity"] * 0.1)
        
        confidence = (base_confidence + mytguy_boost + relationship_boost) * complexity_factor
        return min(max(confidence, 0.3), 0.95)  # Clamp between 0.3 and 0.95
    
    def _extract_mytguy_insights(self, context: DraftContext, analysis: Dict) -> Dict:
        """Extract MyTGuy-specific insights for the draft"""
        insights = {
            "mytguy_relevance_score": analysis["mytguy_relevance"],
            "relationship_context": context.mytguy_relationship,
            "recommended_tone": self._determine_tone(context, analysis).value,
            "business_impact": "medium",
            "follow_up_strategy": "standard"
        }
        
        # Enhance insights based on analysis
        if analysis["mytguy_relevance"] > 0.7:
            insights["business_impact"] = "high"
            insights["follow_up_strategy"] = "proactive"
        
        if context.urgency_level >= 3:
            insights["follow_up_strategy"] = "immediate"
        
        # Add relationship-specific insights
        if context.mytguy_relationship == "client":
            insights["client_satisfaction_focus"] = True
            insights["delivery_timeline_critical"] = context.urgency_level >= 3
        
        return insights
    
    def _suggest_send_time(self, context: DraftContext, analysis: Dict) -> Optional[datetime]:
        """Suggest optimal send time based on context and relationship"""
        now = datetime.now()
        
        # Immediate send for urgent items
        if context.urgency_level >= 4:
            return now
        
        # Business hours optimization
        if now.hour < 9:
            return now.replace(hour=9, minute=0, second=0, microsecond=0)
        elif now.hour > 17:
            return (now + timedelta(days=1)).replace(hour=9, minute=0, second=0, microsecond=0)
        
        # Relationship-based timing
        if context.mytguy_relationship == "prospect" and now.hour > 16:
            # Send prospects emails early in business day
            return (now + timedelta(days=1)).replace(hour=10, minute=0, second=0, microsecond=0)
        
        return now  # Send immediately during business hours
    
    def _needs_follow_up(self, context: DraftContext, analysis: Dict) -> bool:
        """Determine if follow-up is required"""
        return (
            bool(context.required_actions) or
            context.urgency_level >= 3 or
            context.mytguy_relationship == "prospect" or
            analysis["mytguy_relevance"] > 0.7
        )
    
    def _apply_mytguy_enhancements(self, draft: EmailDraft, context: DraftContext, analysis: Dict) -> EmailDraft:
        """Apply MyTGuy-specific enhancements to the draft"""
        # Enhance subject with MyTGuy branding if relevant
        if analysis["mytguy_relevance"] > 0.6 and "MyTGuy" not in draft.subject:
            if context.mytguy_relationship in ["client", "prospect"]:
                draft.subject = f"MyTGuy: {draft.subject}"
        
        # Add MyTGuy signature elements
        if "MyTGuy" not in draft.body:
            mytguy_footer = "\n\n---\nPowered by MyTGuy - Your Technology Partner"
            if analysis["mytguy_relevance"] > 0.7:
                draft.body += mytguy_footer
        
        return draft
    
    def _optimize_for_relationship(self, draft: EmailDraft, context: DraftContext) -> EmailDraft:
        """Optimize draft based on recipient relationship"""
        relationship_config = self.relationship_patterns.get(context.mytguy_relationship, {})
        
        # Adjust formality level
        formality = relationship_config.get("formality", 0.6)
        if formality > 0.7:
            # Make more formal
            draft.body = draft.body.replace("Hi ", "Dear ")
            draft.body = draft.body.replace("Thanks!", "Thank you.")
        elif formality < 0.5:
            # Make more casual
            draft.body = draft.body.replace("Dear ", "Hi ")
            draft.body = draft.body.replace("Thank you.", "Thanks!")
        
        # Adjust detail level
        detail_level = relationship_config.get("detail_level", "medium")
        if detail_level == "high" and len(draft.body.split()) < 100:
            # Add more context for high-detail relationships
            additional_context = "\n\nI'm happy to provide additional details or schedule a call to discuss this further."
            draft.body += additional_context
        
        return draft
    
    def _build_ai_prompt(self, draft_type: DraftType, context: DraftContext, analysis: Dict) -> str:
        """Build comprehensive prompt for AI draft generation"""
        prompt = f"""Generate a professional email draft with the following specifications:

Draft Type: {draft_type.value}
Recipient Relationship: {context.mytguy_relationship}
Urgency Level: {context.urgency_level}/5
MyTGuy Relevance: {analysis['mytguy_relevance']:.2f}

Context:
- Project: {context.project_context or 'General communication'}
- Required Actions: {', '.join(context.required_actions) if context.required_actions else 'None'}
"""
        
        if context.original_email:
            prompt += f"\nOriginal Email Subject: {context.original_email.get('subject', 'N/A')}"
            prompt += f"\nOriginal Email Content: {context.original_email.get('body', 'N/A')[:300]}..."
        
        prompt += f"""

MyTGuy Business Context:
- We are a technology consulting firm specializing in custom development and system integration
- Focus on delivering high-quality, scalable solutions
- Strong emphasis on client relationships and technical excellence

Please generate:
1. An appropriate subject line
2. Professional email body
3. Tone should be {self._determine_tone(context, analysis).value}

Format the response as JSON with 'subject' and 'body' fields."""
        
        return prompt
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for AI generation"""
        return """You are an expert email assistant for MyTGuy, a technology consulting firm. 
You specialize in creating professional, contextually appropriate business communications that reflect our commitment to technical excellence and client success. 
Always maintain a professional tone while being personable and helpful. Focus on clear communication and actionable content."""
    
    def _parse_ai_response(self, response: str, context: DraftContext, analysis: Dict) -> EmailDraft:
        """Parse AI response into EmailDraft object"""
        try:
            # Try to parse as JSON
            if response.strip().startswith('{'):
                data = json.loads(response)
                subject = data.get('subject', 'MyTGuy Communication')
                body = data.get('body', 'Thank you for your message.')
            else:
                # Parse structured text response
                lines = response.split('\n')
                subject = next((line.replace('Subject:', '').strip() for line in lines if line.startswith('Subject:')), 'MyTGuy Communication')
                body_start = next((i for i, line in enumerate(lines) if 'body' in line.lower()), 0) + 1
                body = '\n'.join(lines[body_start:]).strip()
            
            return EmailDraft(
                subject=subject,
                body=body,
                tone_style=self._determine_tone(context, analysis),
                confidence_score=self._calculate_confidence(analysis, "ai"),
                mytguy_insights=self._extract_mytguy_insights(context, analysis),
                suggested_send_time=self._suggest_send_time(context, analysis),
                follow_up_required=self._needs_follow_up(context, analysis),
                priority_level=max(1, int(analysis["urgency_factor"] * 5))
            )
            
        except Exception as e:
            logger.error(f"Failed to parse AI response: {e}")
            return self._generate_rule_based_draft(DraftType.NEW_EMAIL, context, analysis)
    
    def _generate_fallback_draft(self, draft_type: DraftType, context: DraftContext) -> EmailDraft:
        """Generate a simple fallback draft when other methods fail"""
        return EmailDraft(
            subject="Re: " + context.original_email.get('subject', 'Your Email') if context.original_email else "Response",
            body="Thank you for your email. I will review this and get back to you shortly.\n\nBest regards,\nJordan Lewis",
            tone_style=ToneStyle.PROFESSIONAL,
            confidence_score=0.3,
            mytguy_insights={"fallback": True},
            priority_level=2
        )