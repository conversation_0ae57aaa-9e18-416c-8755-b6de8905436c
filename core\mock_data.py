# core/mock_data.py
# Sample email data for testing the summarization engine

from datetime import datetime, timedelta

# Sample emails for testing
SAMPLE_EMAILS = [
    {
        "sender": "<EMAIL>",
        "subject": "Q4 Budget Review - Need Your Input by Friday",
        "body": """Hi [Your Name],

Hope you're doing well. I need your thoughts on the Q4 budget allocations before our meeting with the finance team on Friday.

Key areas I'm particularly concerned about:
• Marketing spend vs. projected ROI
• IT infrastructure upgrades - are they really necessary this quarter?
• Should we increase the training budget given our new hires?

The current draft shows a 15% increase over Q3, which might be aggressive given market conditions. What do you think about scaling back the discretionary spending by 10%?

Also, can you pull together a brief analysis of our vendor contracts? I have a feeling we're overpaying for some services.

Let me know your thoughts by Thursday afternoon so I can incorporate them into the presentation.

Thanks,
MyTGuy""",
        "timestamp": datetime.now() - timedelta(hours=2)
    },
    
    {
        "sender": "<EMAIL>", 
        "subject": "Team Meeting Recap and Action Items",
        "body": """Hi everyone,

Thanks for a productive meeting this morning. Here are the key takeaways and action items:

Discussion Points:
- Project timeline is on track but we need to monitor resource allocation more closely
- Client feedback has been positive overall, with some minor UI concerns
- The integration with the new API is progressing well

Action Items:
1. Sarah - Update project documentation by Wednesday
2. Mike - Schedule follow-up with client by end of week  
3. Team - Review and test new features before Thursday deployment

Next meeting scheduled for next Tuesday at 10 AM.

Best regards,
Colleague""",
        "timestamp": datetime.now() - timedelta(hours=4)
    },
    
    {
        "sender": "<EMAIL>",
        "subject": "Urgent: Client Meeting Moved to Tomorrow",
        "body": """[Your Name],

Quick update - the Johnson client meeting has been moved to tomorrow at 2 PM due to their schedule change.

Can you make sure the presentation includes:
- Updated revenue projections 
- Competitive analysis slide
- Implementation timeline

I know it's short notice, but they're eager to move forward and this could be a significant deal for us. 

Let me know if you need any additional resources to get this ready.

MyTGuy

Sent from my iPhone""",
        "timestamp": datetime.now() - timedelta(minutes=30)
    },
    
    {
        "sender": "<EMAIL>",
        "subject": "Invoice #12345 - Payment Overdue",
        "body": """Dear Customer,

This is a reminder that Invoice #12345 for $2,450.00 was due on [date] and remains unpaid.

Please remit payment within 5 business days to avoid any service interruption.

If you have already sent payment, please disregard this notice.

For questions, contact our billing <NAME_EMAIL>.

Sincerely,
Accounts Receivable
Supplier Company""",
        "timestamp": datetime.now() - timedelta(days=1)
    }
]

def get_sample_email(index: int = 0):
    """Get a sample email by index"""
    if 0 <= index < len(SAMPLE_EMAILS):
        return SAMPLE_EMAILS[index]
    return SAMPLE_EMAILS[0]

def get_all_sample_emails():
    """Get all sample emails"""
    return SAMPLE_EMAILS