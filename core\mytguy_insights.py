#!/usr/bin/env python3
"""
MyTGuy-Specific Insights Module
Part of the MyTGuy Email Agent Architecture

This module provides advanced pattern recognition, communication analysis,
and predictive insights specifically tailored for MyTGuy business operations.
"""

import re
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict, Counter
import openai
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InsightType(Enum):
    """Types of insights that can be generated"""
    COMMUNICATION_PATTERN = "communication_pattern"
    BUSINESS_OPPORTUNITY = "business_opportunity"
    RISK_INDICATOR = "risk_indicator"
    RELATIONSHIP_HEALTH = "relationship_health"
    DECISION_POINT = "decision_point"
    WORKLOAD_ANALYSIS = "workload_analysis"
    CLIENT_SENTIMENT = "client_sentiment"
    PRIORITY_SHIFT = "priority_shift"

class InsightPriority(Enum):
    """Priority levels for insights"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class MyTGuyInsight:
    """Represents a MyTGuy-specific insight"""
    id: str
    insight_type: InsightType
    priority: InsightPriority
    title: str
    description: str
    evidence: List[str]
    recommendations: List[str]
    confidence_score: float
    business_impact: str
    source_emails: List[Dict]
    created_at: datetime
    expires_at: Optional[datetime] = None
    metadata: Dict = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class MyTGuyInsightsEngine:
    """Advanced insights engine with MyTGuy business intelligence"""
    
    def __init__(self, openai_api_key: Optional[str] = None):
        self.openai_client = None
        if openai_api_key or os.getenv('OPENAI_API_KEY'):
            try:
                openai.api_key = openai_api_key or os.getenv('OPENAI_API_KEY')
                self.openai_client = openai
                logger.info("OpenAI client initialized for insights generation")
            except Exception as e:
                logger.warning(f"OpenAI initialization failed: {e}")
        
        # MyTGuy business intelligence patterns
        self.business_patterns = self._load_business_patterns()
        self.communication_patterns = self._load_communication_patterns()
        self.risk_indicators = self._load_risk_indicators()
        self.opportunity_signals = self._load_opportunity_signals()
        
        # Historical data storage (in production, this would be a database)
        self.email_history = []
        self.insight_history = []
        self.relationship_data = defaultdict(dict)
    
    def _load_business_patterns(self) -> Dict:
        """Load MyTGuy-specific business patterns"""
        return {
            "project_lifecycle": {
                "discovery": ["requirements", "scope", "timeline", "budget"],
                "planning": ["architecture", "design", "resources", "milestones"],
                "execution": ["development", "testing", "deployment", "delivery"],
                "maintenance": ["support", "updates", "optimization", "scaling"]
            },
            "client_engagement": {
                "prospecting": ["proposal", "demo", "pricing", "capabilities"],
                "onboarding": ["kickoff", "setup", "training", "integration"],
                "delivery": ["progress", "updates", "feedback", "adjustments"],
                "growth": ["expansion", "additional", "upgrade", "referral"]
            },
            "business_metrics": {
                "revenue_indicators": ["contract", "payment", "invoice", "budget"],
                "growth_signals": ["expansion", "new client", "referral", "upsell"],
                "efficiency_markers": ["automation", "process", "optimization", "streamline"],
                "risk_factors": ["delay", "issue", "concern", "problem", "escalation"]
            }
        }
    
    def _load_communication_patterns(self) -> Dict:
        """Load communication pattern analysis rules"""
        return {
            "urgency_escalation": {
                "patterns": ["urgent", "asap", "immediately", "critical", "emergency"],
                "frequency_threshold": 3,  # emails per week
                "concern_level": "high"
            },
            "decision_bottlenecks": {
                "patterns": ["waiting for", "pending", "need decision", "approval"],
                "frequency_threshold": 2,
                "concern_level": "medium"
            },
            "client_satisfaction": {
                "positive_signals": ["great", "excellent", "satisfied", "happy", "impressed"],
                "negative_signals": ["disappointed", "concerned", "issue", "problem", "unhappy"],
                "neutral_signals": ["okay", "fine", "acceptable", "standard"]
            },
            "workload_indicators": {
                "overload_signals": ["overwhelmed", "too much", "capacity", "bandwidth"],
                "underutilized_signals": ["available", "free time", "capacity", "looking for"]
            }
        }
    
    def _load_risk_indicators(self) -> Dict:
        """Load risk detection patterns"""
        return {
            "client_risk": {
                "payment_delays": ["overdue", "payment", "invoice", "outstanding"],
                "scope_creep": ["additional", "extra", "beyond scope", "not included"],
                "dissatisfaction": ["unhappy", "disappointed", "not meeting", "concerns"],
                "competition": ["competitor", "alternative", "other vendor", "proposal"]
            },
            "project_risk": {
                "timeline_risk": ["behind", "delayed", "late", "deadline", "rushing"],
                "resource_risk": ["understaffed", "overloaded", "capacity", "availability"],
                "technical_risk": ["complex", "challenging", "unknown", "experimental"],
                "budget_risk": ["over budget", "additional cost", "expensive", "funding"]
            },
            "business_risk": {
                "market_changes": ["economic", "market", "industry", "competition"],
                "operational_risk": ["process", "system", "infrastructure", "security"],
                "talent_risk": ["hiring", "retention", "skills", "training"]
            }
        }
    
    def _load_opportunity_signals(self) -> Dict:
        """Load opportunity detection patterns"""
        return {
            "expansion_opportunities": {
                "upsell_signals": ["additional", "more", "expand", "upgrade", "enhance"],
                "cross_sell_signals": ["other services", "different", "also need", "what else"],
                "referral_signals": ["recommend", "know someone", "other companies", "similar"]
            },
            "new_business": {
                "inquiry_signals": ["interested", "proposal", "quote", "capabilities"],
                "partnership_signals": ["partner", "collaborate", "joint", "together"],
                "market_signals": ["opportunity", "new market", "expansion", "growth"]
            },
            "innovation_opportunities": {
                "technology_signals": ["new technology", "innovation", "automation", "AI"],
                "process_signals": ["improve", "optimize", "streamline", "efficiency"],
                "service_signals": ["new service", "offering", "capability", "solution"]
            }
        }
    
    def analyze_email_for_insights(self, email_data: Dict, context: Dict = None) -> List[MyTGuyInsight]:
        """Analyze a single email for MyTGuy-specific insights"""
        try:
            # Store email in history for pattern analysis
            self.email_history.append({
                **email_data,
                'analyzed_at': datetime.now()
            })
            
            insights = []
            
            # Generate different types of insights
            insights.extend(self._analyze_communication_patterns(email_data))
            insights.extend(self._analyze_business_opportunities(email_data))
            insights.extend(self._analyze_risk_indicators(email_data))
            insights.extend(self._analyze_relationship_health(email_data))
            insights.extend(self._analyze_decision_points(email_data))
            
            # Use AI for advanced insights if available
            if self.openai_client:
                ai_insights = self._generate_ai_insights(email_data, context)
                insights.extend(ai_insights)
            
            # Filter and prioritize insights
            filtered_insights = self._filter_and_prioritize_insights(insights)
            
            logger.info(f"Generated {len(filtered_insights)} insights from email: {email_data.get('subject', '')}")
            return filtered_insights
            
        except Exception as e:
            logger.error(f"Insight analysis failed: {e}")
            return []
    
    def analyze_email_batch_for_trends(self, emails: List[Dict], timeframe_days: int = 30) -> List[MyTGuyInsight]:
        """Analyze multiple emails for trends and patterns"""
        try:
            # Filter emails by timeframe
            cutoff_date = datetime.now() - timedelta(days=timeframe_days)
            recent_emails = [
                email for email in emails
                if email.get('timestamp', datetime.now()) >= cutoff_date
            ]
            
            insights = []
            
            # Trend analysis
            insights.extend(self._analyze_communication_trends(recent_emails))
            insights.extend(self._analyze_workload_trends(recent_emails))
            insights.extend(self._analyze_client_sentiment_trends(recent_emails))
            insights.extend(self._analyze_priority_shifts(recent_emails))
            
            # Cross-email pattern detection
            insights.extend(self._detect_cross_email_patterns(recent_emails))
            
            logger.info(f"Generated {len(insights)} trend insights from {len(recent_emails)} emails")
            return insights
            
        except Exception as e:
            logger.error(f"Batch insight analysis failed: {e}")
            return []
    
    def _analyze_communication_patterns(self, email_data: Dict) -> List[MyTGuyInsight]:
        """Analyze communication patterns in the email"""
        insights = []
        sender = email_data.get('sender', '')
        subject = email_data.get('subject', '')
        body = email_data.get('body', '')
        content = f"{subject} {body}".lower()
        
        # Check for urgency escalation
        urgency_count = sum(1 for pattern in self.communication_patterns["urgency_escalation"]["patterns"]
                           if pattern in content)
        
        if urgency_count >= 2:
            insights.append(MyTGuyInsight(
                id=f"comm_urgency_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                insight_type=InsightType.COMMUNICATION_PATTERN,
                priority=InsightPriority.HIGH,
                title="High Urgency Communication Detected",
                description=f"Email contains {urgency_count} urgency indicators, suggesting time-sensitive matters.",
                evidence=[f"Urgency indicators found: {urgency_count}"],
                recommendations=[
                    "Prioritize immediate response",
                    "Clarify timeline expectations",
                    "Consider scheduling urgent call if needed"
                ],
                confidence_score=0.8,
                business_impact="May indicate critical business situation requiring immediate attention",
                source_emails=[email_data],
                created_at=datetime.now()
            ))
        
        # Check for decision bottlenecks
        decision_patterns = self.communication_patterns["decision_bottlenecks"]["patterns"]
        decision_count = sum(1 for pattern in decision_patterns if pattern in content)
        
        if decision_count >= 1:
            insights.append(MyTGuyInsight(
                id=f"comm_decision_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                insight_type=InsightType.DECISION_POINT,
                priority=InsightPriority.MEDIUM,
                title="Decision Point Identified",
                description="Email indicates pending decisions or approvals needed.",
                evidence=[f"Decision indicators: {decision_count}"],
                recommendations=[
                    "Identify specific decisions needed",
                    "Set decision timeline",
                    "Gather necessary information for decision-making"
                ],
                confidence_score=0.7,
                business_impact="Delayed decisions may impact project timelines and client satisfaction",
                source_emails=[email_data],
                created_at=datetime.now()
            ))
        
        return insights
    
    def _analyze_business_opportunities(self, email_data: Dict) -> List[MyTGuyInsight]:
        """Analyze email for business opportunities"""
        insights = []
        content = f"{email_data.get('subject', '')} {email_data.get('body', '')}".lower()
        
        # Check for expansion opportunities
        expansion_signals = self.opportunity_signals["expansion_opportunities"]
        upsell_count = sum(1 for signal in expansion_signals["upsell_signals"] if signal in content)
        
        if upsell_count >= 1:
            insights.append(MyTGuyInsight(
                id=f"opp_upsell_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                insight_type=InsightType.BUSINESS_OPPORTUNITY,
                priority=InsightPriority.HIGH,
                title="Potential Upsell Opportunity Detected",
                description="Client communication suggests interest in additional services or expansion.",
                evidence=[f"Upsell signals detected: {upsell_count}"],
                recommendations=[
                    "Prepare expanded service proposal",
                    "Schedule discovery call for additional needs",
                    "Review client's current engagement for expansion possibilities"
                ],
                confidence_score=0.75,
                business_impact="Could lead to increased revenue and deeper client relationship",
                source_emails=[email_data],
                created_at=datetime.now()
            ))
        
        # Check for referral opportunities
        referral_signals = expansion_signals["referral_signals"]
        referral_count = sum(1 for signal in referral_signals if signal in content)
        
        if referral_count >= 1:
            insights.append(MyTGuyInsight(
                id=f"opp_referral_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                insight_type=InsightType.BUSINESS_OPPORTUNITY,
                priority=InsightPriority.MEDIUM,
                title="Potential Referral Opportunity",
                description="Client may be willing to provide referrals or recommendations.",
                evidence=[f"Referral signals: {referral_count}"],
                recommendations=[
                    "Ask for specific referral introductions",
                    "Provide referral incentive program information",
                    "Request testimonial or case study"
                ],
                confidence_score=0.6,
                business_impact="Referrals are high-quality leads with higher conversion rates",
                source_emails=[email_data],
                created_at=datetime.now()
            ))
        
        return insights
    
    def _analyze_risk_indicators(self, email_data: Dict) -> List[MyTGuyInsight]:
        """Analyze email for risk indicators"""
        insights = []
        content = f"{email_data.get('subject', '')} {email_data.get('body', '')}".lower()
        
        # Check for client risk indicators
        client_risks = self.risk_indicators["client_risk"]
        
        # Payment risk
        payment_risk_count = sum(1 for indicator in client_risks["payment_delays"] if indicator in content)
        if payment_risk_count >= 1:
            insights.append(MyTGuyInsight(
                id=f"risk_payment_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                insight_type=InsightType.RISK_INDICATOR,
                priority=InsightPriority.HIGH,
                title="Payment Risk Detected",
                description="Email indicates potential payment delays or issues.",
                evidence=[f"Payment risk indicators: {payment_risk_count}"],
                recommendations=[
                    "Follow up on outstanding invoices immediately",
                    "Review payment terms and history",
                    "Consider requiring payment before continuing work"
                ],
                confidence_score=0.85,
                business_impact="Payment delays can impact cash flow and business operations",
                source_emails=[email_data],
                created_at=datetime.now()
            ))
        
        # Scope creep risk
        scope_risk_count = sum(1 for indicator in client_risks["scope_creep"] if indicator in content)
        if scope_risk_count >= 1:
            insights.append(MyTGuyInsight(
                id=f"risk_scope_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                insight_type=InsightType.RISK_INDICATOR,
                priority=InsightPriority.MEDIUM,
                title="Scope Creep Risk Identified",
                description="Client requests may be expanding beyond original project scope.",
                evidence=[f"Scope expansion indicators: {scope_risk_count}"],
                recommendations=[
                    "Review original project scope and contract",
                    "Document additional requests as change orders",
                    "Communicate scope boundaries clearly"
                ],
                confidence_score=0.7,
                business_impact="Unmanaged scope creep can reduce project profitability",
                source_emails=[email_data],
                created_at=datetime.now()
            ))
        
        # Project timeline risk
        project_risks = self.risk_indicators["project_risk"]
        timeline_risk_count = sum(1 for indicator in project_risks["timeline_risk"] if indicator in content)
        
        if timeline_risk_count >= 1:
            insights.append(MyTGuyInsight(
                id=f"risk_timeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                insight_type=InsightType.RISK_INDICATOR,
                priority=InsightPriority.HIGH,
                title="Project Timeline Risk",
                description="Email suggests potential delays or timeline pressures.",
                evidence=[f"Timeline risk indicators: {timeline_risk_count}"],
                recommendations=[
                    "Review project timeline and milestones",
                    "Identify bottlenecks and resource constraints",
                    "Communicate realistic timeline expectations"
                ],
                confidence_score=0.8,
                business_impact="Timeline delays can affect client satisfaction and future business",
                source_emails=[email_data],
                created_at=datetime.now()
            ))
        
        return insights
    
    def _analyze_relationship_health(self, email_data: Dict) -> List[MyTGuyInsight]:
        """Analyze relationship health indicators"""
        insights = []
        content = f"{email_data.get('subject', '')} {email_data.get('body', '')}".lower()
        sender = email_data.get('sender', '')
        
        # Analyze sentiment indicators
        positive_signals = self.communication_patterns["client_satisfaction"]["positive_signals"]
        negative_signals = self.communication_patterns["client_satisfaction"]["negative_signals"]
        
        positive_count = sum(1 for signal in positive_signals if signal in content)
        negative_count = sum(1 for signal in negative_signals if signal in content)
        
        if negative_count > positive_count and negative_count >= 2:
            insights.append(MyTGuyInsight(
                id=f"rel_negative_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                insight_type=InsightType.RELATIONSHIP_HEALTH,
                priority=InsightPriority.HIGH,
                title="Relationship Health Concern",
                description="Email contains negative sentiment indicators suggesting client dissatisfaction.",
                evidence=[f"Negative sentiment indicators: {negative_count}"],
                recommendations=[
                    "Schedule immediate call to address concerns",
                    "Identify specific issues and resolution plans",
                    "Consider escalating to senior team member"
                ],
                confidence_score=0.75,
                business_impact="Poor relationship health can lead to client churn and reputation damage",
                source_emails=[email_data],
                created_at=datetime.now()
            ))
        elif positive_count >= 2 and positive_count > negative_count:
            insights.append(MyTGuyInsight(
                id=f"rel_positive_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                insight_type=InsightType.RELATIONSHIP_HEALTH,
                priority=InsightPriority.LOW,
                title="Strong Relationship Health",
                description="Email shows positive sentiment and client satisfaction.",
                evidence=[f"Positive sentiment indicators: {positive_count}"],
                recommendations=[
                    "Leverage positive relationship for referrals",
                    "Consider upselling opportunities",
                    "Request testimonial or case study"
                ],
                confidence_score=0.8,
                business_impact="Strong relationships lead to retention, referrals, and growth",
                source_emails=[email_data],
                created_at=datetime.now()
            ))
        
        return insights
    
    def _analyze_decision_points(self, email_data: Dict) -> List[MyTGuyInsight]:
        """Analyze email for decision points requiring attention"""
        insights = []
        content = f"{email_data.get('subject', '')} {email_data.get('body', '')}".lower()
        
        # Decision keywords
        decision_keywords = [
            "decide", "decision", "choose", "select", "approve", "reject",
            "go/no-go", "proceed", "move forward", "what do you think"
        ]
        
        decision_count = sum(1 for keyword in decision_keywords if keyword in content)
        
        if decision_count >= 1:
            # Extract potential decision context
            decision_context = self._extract_decision_context(email_data.get('body', ''))
            
            insights.append(MyTGuyInsight(
                id=f"decision_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                insight_type=InsightType.DECISION_POINT,
                priority=InsightPriority.HIGH,
                title="Critical Decision Point Identified",
                description="Email requires important business decision or approval.",
                evidence=[f"Decision indicators: {decision_count}", f"Context: {decision_context}"],
                recommendations=[
                    "Gather all relevant information for decision",
                    "Set decision deadline",
                    "Identify stakeholders who need to be involved",
                    "Document decision rationale"
                ],
                confidence_score=0.85,
                business_impact="Timely decisions are critical for business momentum and client satisfaction",
                source_emails=[email_data],
                created_at=datetime.now(),
                metadata={"decision_context": decision_context}
            ))
        
        return insights
    
    def _extract_decision_context(self, email_body: str) -> str:
        """Extract context around decision points"""
        # Look for sentences containing decision keywords
        sentences = email_body.split('.')
        decision_sentences = []
        
        decision_keywords = ["decide", "decision", "choose", "select", "approve"]
        
        for sentence in sentences:
            if any(keyword in sentence.lower() for keyword in decision_keywords):
                decision_sentences.append(sentence.strip())
        
        return " ".join(decision_sentences[:2])  # Return first 2 relevant sentences
    
    def _analyze_communication_trends(self, emails: List[Dict]) -> List[MyTGuyInsight]:
        """Analyze communication trends across multiple emails"""
        insights = []
        
        if len(emails) < 5:  # Need sufficient data for trend analysis
            return insights
        
        # Analyze email frequency by sender
        sender_counts = Counter(email.get('sender', '') for email in emails)
        
        # Identify high-frequency communicators
        for sender, count in sender_counts.most_common(3):
            if count >= 5:  # 5+ emails in timeframe
                insights.append(MyTGuyInsight(
                    id=f"trend_freq_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(sender) % 1000}",
                    insight_type=InsightType.COMMUNICATION_PATTERN,
                    priority=InsightPriority.MEDIUM,
                    title=f"High Communication Frequency: {sender}",
                    description=f"Sender has sent {count} emails recently, indicating active engagement or potential issues.",
                    evidence=[f"Email count: {count} emails"],
                    recommendations=[
                        "Review communication for patterns or concerns",
                        "Consider scheduling call to address multiple topics at once",
                        "Evaluate if high frequency indicates satisfaction or problems"
                    ],
                    confidence_score=0.9,
                    business_impact="High communication frequency may indicate either strong engagement or emerging issues",
                    source_emails=[email for email in emails if email.get('sender') == sender],
                    created_at=datetime.now()
                ))
        
        return insights
    
    def _analyze_workload_trends(self, emails: List[Dict]) -> List[MyTGuyInsight]:
        """Analyze workload trends from email patterns"""
        insights = []
        
        # Count action items and tasks across emails
        total_action_items = 0
        urgent_items = 0
        
        for email in emails:
            content = f"{email.get('subject', '')} {email.get('body', '')}".lower()
            
            # Count action indicators
            action_indicators = ["please", "need", "can you", "could you", "action item", "task"]
            total_action_items += sum(1 for indicator in action_indicators if indicator in content)
            
            # Count urgency indicators
            urgency_indicators = ["urgent", "asap", "immediately", "critical"]
            urgent_items += sum(1 for indicator in urgency_indicators if indicator in content)
        
        # Analyze workload level
        avg_actions_per_email = total_action_items / len(emails) if emails else 0
        urgency_ratio = urgent_items / total_action_items if total_action_items > 0 else 0
        
        if avg_actions_per_email > 2:  # High action item density
            insights.append(MyTGuyInsight(
                id=f"workload_high_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                insight_type=InsightType.WORKLOAD_ANALYSIS,
                priority=InsightPriority.HIGH,
                title="High Workload Detected",
                description=f"Recent emails show high action item density ({avg_actions_per_email:.1f} per email).",
                evidence=[f"Total action items: {total_action_items}", f"Average per email: {avg_actions_per_email:.1f}"],
                recommendations=[
                    "Prioritize tasks by business impact",
                    "Consider delegating non-critical items",
                    "Schedule focused work blocks for high-priority tasks"
                ],
                confidence_score=0.8,
                business_impact="High workload may lead to burnout and reduced quality of work",
                source_emails=emails,
                created_at=datetime.now()
            ))
        
        if urgency_ratio > 0.3:  # High urgency ratio
            insights.append(MyTGuyInsight(
                id=f"urgency_high_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                insight_type=InsightType.PRIORITY_SHIFT,
                priority=InsightPriority.HIGH,
                title="High Urgency Pattern Detected",
                description=f"High percentage of urgent items ({urgency_ratio:.1%}) suggests reactive work mode.",
                evidence=[f"Urgent items: {urgent_items}", f"Urgency ratio: {urgency_ratio:.1%}"],
                recommendations=[
                    "Analyze root causes of urgency",
                    "Implement better planning and forecasting",
                    "Set clearer expectations with clients on timelines"
                ],
                confidence_score=0.85,
                business_impact="Constant urgency can reduce efficiency and increase stress",
                source_emails=emails,
                created_at=datetime.now()
            ))
        
        return insights
    
    def _analyze_client_sentiment_trends(self, emails: List[Dict]) -> List[MyTGuyInsight]:
        """Analyze client sentiment trends over time"""
        insights = []
        
        # Group emails by sender and analyze sentiment progression
        sender_emails = defaultdict(list)
        for email in emails:
            sender_emails[email.get('sender', '')].append(email)
        
        for sender, sender_email_list in sender_emails.items():
            if len(sender_email_list) < 3:  # Need multiple emails for trend
                continue
            
            # Sort by timestamp
            sorted_emails = sorted(sender_email_list, key=lambda x: x.get('timestamp', datetime.now()))
            
            # Analyze sentiment progression
            sentiment_scores = []
            for email in sorted_emails:
                content = f"{email.get('subject', '')} {email.get('body', '')}".lower()
                
                positive_signals = self.communication_patterns["client_satisfaction"]["positive_signals"]
                negative_signals = self.communication_patterns["client_satisfaction"]["negative_signals"]
                
                positive_count = sum(1 for signal in positive_signals if signal in content)
                negative_count = sum(1 for signal in negative_signals if signal in content)
                
                # Simple sentiment score (-1 to 1)
                sentiment_score = (positive_count - negative_count) / max(1, positive_count + negative_count)
                sentiment_scores.append(sentiment_score)
            
            # Check for declining sentiment
            if len(sentiment_scores) >= 3:
                recent_avg = sum(sentiment_scores[-2:]) / 2
                earlier_avg = sum(sentiment_scores[:-2]) / len(sentiment_scores[:-2])
                
                if recent_avg < earlier_avg - 0.3:  # Significant decline
                    insights.append(MyTGuyInsight(
                        id=f"sentiment_decline_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(sender) % 1000}",
                        insight_type=InsightType.CLIENT_SENTIMENT,
                        priority=InsightPriority.HIGH,
                        title=f"Declining Client Sentiment: {sender}",
                        description="Client sentiment appears to be declining based on recent communication patterns.",
                        evidence=[f"Sentiment trend: {earlier_avg:.2f} → {recent_avg:.2f}"],
                        recommendations=[
                            "Schedule immediate check-in call with client",
                            "Review recent project deliverables and feedback",
                            "Identify and address specific concerns proactively"
                        ],
                        confidence_score=0.7,
                        business_impact="Declining sentiment may lead to client churn or negative reviews",
                        source_emails=sorted_emails,
                        created_at=datetime.now()
                    ))
        
        return insights
    
    def _analyze_priority_shifts(self, emails: List[Dict]) -> List[MyTGuyInsight]:
        """Analyze shifts in priority and focus areas"""
        insights = []
        
        # Track topic frequency over time
        business_topics = {
            "technical": ["development", "code", "technical", "architecture", "implementation"],
            "business": ["revenue", "budget", "contract", "proposal", "business"],
            "client": ["client", "customer", "meeting", "feedback", "satisfaction"],
            "operations": ["process", "workflow", "efficiency", "operations", "management"]
        }
        
        # Split emails into early and recent periods
        sorted_emails = sorted(emails, key=lambda x: x.get('timestamp', datetime.now()))
        mid_point = len(sorted_emails) // 2
        
        early_emails = sorted_emails[:mid_point]
        recent_emails = sorted_emails[mid_point:]
        
        # Calculate topic frequencies for each period
        early_topics = self._calculate_topic_frequencies(early_emails, business_topics)
        recent_topics = self._calculate_topic_frequencies(recent_emails, business_topics)
        
        # Identify significant shifts
        for topic, recent_freq in recent_topics.items():
            early_freq = early_topics.get(topic, 0)
            
            if recent_freq > early_freq * 2 and recent_freq > 0.2:  # Significant increase
                insights.append(MyTGuyInsight(
                    id=f"priority_shift_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{topic}",
                    insight_type=InsightType.PRIORITY_SHIFT,
                    priority=InsightPriority.MEDIUM,
                    title=f"Increased Focus on {topic.title()} Topics",
                    description=f"Recent emails show increased focus on {topic} topics ({recent_freq:.1%} vs {early_freq:.1%}).",
                    evidence=[f"Early frequency: {early_freq:.1%}", f"Recent frequency: {recent_freq:.1%}"],
                    recommendations=[
                        f"Ensure adequate resources allocated to {topic} areas",
                        f"Review {topic} strategy and priorities",
                        f"Consider if {topic} focus shift aligns with business goals"
                    ],
                    confidence_score=0.75,
                    business_impact=f"Shift in {topic} focus may indicate changing business priorities or emerging challenges",
                    source_emails=recent_emails,
                    created_at=datetime.now()
                ))
        
        return insights
    
    def _calculate_topic_frequencies(self, emails: List[Dict], topics: Dict[str, List[str]]) -> Dict[str, float]:
        """Calculate frequency of topics in email set"""
        topic_counts = {topic: 0 for topic in topics.keys()}
        total_emails = len(emails)
        
        if total_emails == 0:
            return topic_counts
        
        for email in emails:
            content = f"{email.get('subject', '')} {email.get('body', '')}".lower()
            
            for topic, keywords in topics.items():
                if any(keyword in content for keyword in keywords):
                    topic_counts[topic] += 1
        
        # Convert to frequencies
        return {topic: count / total_emails for topic, count in topic_counts.items()}
    
    def _detect_cross_email_patterns(self, emails: List[Dict]) -> List[MyTGuyInsight]:
        """Detect patterns that span multiple emails"""
        insights = []
        
        # Look for recurring themes or issues
        all_content = " ".join([f"{email.get('subject', '')} {email.get('body', '')}" for email in emails]).lower()
        
        # Common business issue patterns
        issue_patterns = {
            "resource_constraints": ["capacity", "bandwidth", "overloaded", "too much work"],
            "timeline_pressure": ["deadline", "behind", "delayed", "rushing", "time pressure"],
            "communication_gaps": ["unclear", "confused", "misunderstanding", "clarification"],
            "scope_management": ["scope", "additional", "extra", "beyond", "not included"]
        }
        
        for issue_type, patterns in issue_patterns.items():
            pattern_count = sum(1 for pattern in patterns if pattern in all_content)
            
            if pattern_count >= 3:  # Multiple mentions across emails
                insights.append(MyTGuyInsight(
                    id=f"pattern_{issue_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    insight_type=InsightType.COMMUNICATION_PATTERN,
                    priority=InsightPriority.HIGH,
                    title=f"Recurring {issue_type.replace('_', ' ').title()} Pattern",
                    description=f"Multiple emails mention {issue_type.replace('_', ' ')} issues, indicating systemic concern.",
                    evidence=[f"Pattern mentions: {pattern_count}"],
                    recommendations=[
                        f"Address {issue_type.replace('_', ' ')} systematically",
                        "Identify root causes and implement solutions",
                        "Monitor for improvement in future communications"
                    ],
                    confidence_score=0.8,
                    business_impact=f"Recurring {issue_type.replace('_', ' ')} issues can compound and affect business performance",
                    source_emails=emails,
                    created_at=datetime.now()
                ))
        
        return insights
    
    def _generate_ai_insights(self, email_data: Dict, context: Dict = None) -> List[MyTGuyInsight]:
        """Generate insights using AI analysis"""
        try:
            prompt = self._build_insights_prompt(email_data, context)
            
            response = self.openai_client.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self._get_insights_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.3
            )
            
            ai_response = response.choices[0].message.content
            insights = self._parse_ai_insights_response(ai_response, email_data)
            
            return insights
            
        except Exception as e:
            logger.error(f"AI insights generation failed: {e}")
            return []
    
    def _build_insights_prompt(self, email_data: Dict, context: Dict = None) -> str:
        """Build prompt for AI insights generation"""
        context_info = ""
        if context:
            context_info = f"Additional context: {json.dumps(context, default=str)}"
        
        return f"""
        Analyze this email for MyTGuy business insights:
        
        From: {email_data.get('sender', '')}
        Subject: {email_data.get('subject', '')}
        
        {context_info}
        
        Email Body:
        {email_data.get('body', '')}
        
        Identify:
        1. Business opportunities (upsell, referral, expansion)
        2. Risk indicators (payment, scope, timeline, relationship)
        3. Decision points requiring attention
        4. Communication patterns or concerns
        5. Strategic insights for MyTGuy business
        
        Focus on actionable insights with business impact.
        """
    
    def _get_insights_system_prompt(self) -> str:
        """Get system prompt for AI insights generation"""
        return """
        You are a business intelligence analyst for MyTGuy, specializing in email communication analysis.
        
        Generate actionable business insights from email communications. Focus on:
        - Revenue opportunities and business growth
        - Risk mitigation and early warning signals
        - Relationship health and client satisfaction
        - Operational efficiency and process improvements
        - Strategic decision support
        
        Provide specific, actionable recommendations with clear business impact.
        """
    
    def _parse_ai_insights_response(self, ai_response: str, email_data: Dict) -> List[MyTGuyInsight]:
        """Parse AI response into MyTGuyInsight objects"""
        insights = []
        
        # Simple parsing - in production, would use more sophisticated NLP
        lines = ai_response.split('\n')
        current_insight = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Look for insight indicators
            if any(indicator in line.lower() for indicator in ['opportunity:', 'risk:', 'insight:', 'recommendation:']):
                if current_insight:
                    insights.append(current_insight)
                
                # Create new insight
                insight_id = f"ai_insight_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(insights)}"
                current_insight = MyTGuyInsight(
                    id=insight_id,
                    insight_type=InsightType.BUSINESS_OPPORTUNITY,  # Default, would be refined
                    priority=InsightPriority.MEDIUM,
                    title=line[:100],
                    description=line,
                    evidence=["AI analysis"],
                    recommendations=[],
                    confidence_score=0.7,
                    business_impact="AI-identified insight requiring review",
                    source_emails=[email_data],
                    created_at=datetime.now()
                )
            elif current_insight:
                # Add to current insight description
                current_insight.description += f" {line}"
        
        # Add the last insight
        if current_insight:
            insights.append(current_insight)
        
        return insights
    
    def _filter_and_prioritize_insights(self, insights: List[MyTGuyInsight]) -> List[MyTGuyInsight]:
        """Filter and prioritize insights for relevance and importance"""
        # Remove duplicates based on similar titles
        unique_insights = []
        seen_titles = set()
        
        for insight in insights:
            title_key = insight.title.lower()[:50]  # First 50 chars for similarity
            if title_key not in seen_titles:
                unique_insights.append(insight)
                seen_titles.add(title_key)
        
        # Sort by priority and confidence
        sorted_insights = sorted(
            unique_insights,
            key=lambda x: (x.priority.value, x.confidence_score),
            reverse=True
        )
        
        # Limit to top insights to avoid overwhelming user
        return sorted_insights[:10]

def main():
    """Demonstration of the MyTGuy insights engine"""
    print("=== MyTGuy Email Agent - Insights Engine Demo ===\n")
    
    # Initialize insights engine
    engine = MyTGuyInsightsEngine()
    
    # Sample email for testing
    sample_email = {
        "sender": "<EMAIL>",
        "subject": "Concerns about project timeline and additional requirements",
        "body": """Hi Jordan,
        
        I wanted to discuss some concerns about our current project. We're a bit worried about the timeline - it seems like we might be behind schedule, and the deadline is approaching quickly.
        
        Additionally, we've identified some additional requirements that weren't in the original scope:
        - Integration with our legacy CRM system
        - Additional reporting features
        - Mobile app component
        
        We're also considering expanding this project to include our European operations, which could be a significant opportunity.
        
        However, I need to be honest - there have been some internal discussions about budget constraints, and we may need to review the payment schedule.
        
        Can we schedule a call to discuss these items? This is quite urgent as we have a board meeting next week.
        
        Best regards,
        Client""",
        "timestamp": datetime.now()
    }
    
    # Generate insights
    print("Analyzing email for MyTGuy insights...")
    insights = engine.analyze_email_for_insights(sample_email)
    
    # Display results
    print(f"\nGenerated {len(insights)} insights:\n")
    
    for i, insight in enumerate(insights, 1):
        print(f"--- Insight {i} ---")
        print(f"Type: {insight.insight_type.value}")
        print(f"Priority: {insight.priority.name}")
        print(f"Title: {insight.title}")
        print(f"Description: {insight.description}")
        print(f"Business Impact: {insight.business_impact}")
        print(f"Confidence: {insight.confidence_score:.2f}")
        
        if insight.evidence:
            print(f"Evidence: {', '.join(insight.evidence)}")
        
        if insight.recommendations:
            print("Recommendations:")
            for rec in insight.recommendations:
                print(f"  • {rec}")
        
        print()

if __name__ == "__main__":
    main()

