#!/usr/bin/env python3
"""
Email Agent Dashboard API Routes
Provides REST API endpoints for the email agent dashboard
"""

import os
import sys
import json
import threading
from datetime import datetime, timedelta
from flask import Blueprint, jsonify, request, current_app

# Add parent directories to path to import email agent modules
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)
dashboard_dir = os.path.dirname(src_dir)
project_root = os.path.dirname(dashboard_dir)

# Add project root to Python path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from email_processor import EmailProcessor
    from integrated_agent import IntegratedEmailAgent
    from core.graph_api_client import GraphAPIClient
    from config import AgentConfig
    EMAIL_MODULES_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Email modules not available: {e}")
    EMAIL_MODULES_AVAILABLE = False
    # Create a mock AgentConfig for demo mode
    class AgentConfig:
        CLIENT_ID = ""
        CLIENT_SECRET = ""
        TENANT_ID = "common"
        USER_EMAIL = "<EMAIL>"
        USER_NAME = "Demo User"
        COMPANY_DOMAIN = "example.com"
        EMAIL_PROCESSING = {
            "monitor_interval": 60,
            "max_emails_per_check": 20,
            "process_unread_only": True,
            "mark_processed_as_read": False,
            "auto_response_enabled": True,
            "auto_task_extraction": True,
            "auto_meeting_scheduling": True,
            "priority_threshold_for_auto_response": 7,
        }
        AUTO_RESPONSE_CONFIG = {
            "enabled": True,
            "response_delay": 300,
            "include_analysis_summary": True,
            "include_next_steps": True,
            "professional_tone": True,
            "include_signature": True
        }
        SCHEDULING_CONFIG = {
            "working_hours": {"start": "09:00", "end": "17:00"},
            "timezone": "EST",
            "preferred_meeting_times": {
                "client_calls": ["10:00", "14:00"],
                "internal_meetings": ["09:00", "15:00"]
            },
            "buffer_time": {"before": 15, "after": 15},
            "avoid_times": {
                "lunch": {"start": "12:00", "end": "13:00"}
            }
        }

# Create blueprint
agent_bp = Blueprint('agent', __name__)

# Global variables for the email processing system
email_processor = None
processing_thread = None
processing_status = {
    "is_running": False,
    "last_check": None,
    "total_processed": 0,
    "errors": []
}

def initialize_email_processor():
    """Initialize the email processor"""
    global email_processor
    
    if not EMAIL_MODULES_AVAILABLE:
        return None, "Email modules not available"
    
    try:
        # Check if credentials are configured
        if not AgentConfig.CLIENT_ID or not AgentConfig.CLIENT_SECRET:
            return None, "Azure credentials not configured"
        
        # Initialize components
        try:
            graph_client = GraphAPIClient(
                AgentConfig.CLIENT_ID,
                AgentConfig.CLIENT_SECRET,
                AgentConfig.TENANT_ID
            )
        except Exception as e:
            return None, f"Failed to initialize GraphAPIClient: {str(e)}"
        
        try:
            email_agent = IntegratedEmailAgent()
        except Exception as e:
            return None, f"Failed to initialize IntegratedEmailAgent: {str(e)}"
        
        try:
            email_processor = EmailProcessor(graph_client, email_agent)
        except Exception as e:
            return None, f"Failed to initialize EmailProcessor: {str(e)}"
        
        # Set up callbacks
        email_processor.set_email_analyzed_callback(on_email_analyzed)
        
        return email_processor, None
        
    except Exception as e:
        return None, str(e)

def on_email_analyzed(processed_email):
    """Callback for when an email is analyzed"""
    global processing_status
    
    processing_status["total_processed"] += 1
    processing_status["last_check"] = datetime.now().isoformat()

@agent_bp.route('/status', methods=['GET'])
def get_status():
    """Get current agent status"""
    try:
        global email_processor, processing_status

        print("Environment variables:", dict(os.environ))
        
        status = {
            "agent_name": "MyTGuy Email Agent" if EMAIL_MODULES_AVAILABLE else "MyTGuy Email Agent (Demo Mode)",
            "version": "1.0.0",
            "is_configured": EMAIL_MODULES_AVAILABLE and bool(os.getenv('AZURE_CLIENT_ID') and os.getenv('AZURE_CLIENT_SECRET')),
            "is_running": processing_status["is_running"],
            "last_check": processing_status["last_check"],
            "total_processed": processing_status["total_processed"],
            "errors": processing_status["errors"][-5:],  # Last 5 errors
            "timestamp": datetime.now().isoformat(),
            "modules_available": EMAIL_MODULES_AVAILABLE
        }
        
        # Add processing summary if processor is available
        if email_processor and EMAIL_MODULES_AVAILABLE:
            try:
                summary = email_processor.get_processing_summary()
                status.update(summary)
            except Exception as e:
                status["errors"].append(f"Failed to get processing summary: {str(e)}")
        
        return jsonify(status)
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@agent_bp.route('/start', methods=['POST'])
def start_processing():
    """Start email processing"""
    try:
        global email_processor, processing_status, processing_thread
        
        if processing_status["is_running"]:
            return jsonify({"message": "Email processing is already running"}), 400
        
        # Initialize processor if not already done
        if not email_processor:
            processor, error = initialize_email_processor()
            if error:
                return jsonify({"error": error}), 500
            email_processor = processor
        
        # Try to load existing credentials
        credentials_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data", "graph_credentials.json")
        
        if not email_processor.graph_client.load_credentials(credentials_file):
            return jsonify({
                "error": "Authentication required",
                "auth_required": True,
                "message": "Please authenticate with Microsoft Graph API first"
            }), 401
        
        # Start processing in background thread
        def run_processor():
            try:
                email_processor.start_processing()
                processing_status["is_running"] = True
            except Exception as e:
                processing_status["errors"].append(f"Processing error: {str(e)}")
                processing_status["is_running"] = False
        
        processing_thread = threading.Thread(target=run_processor)
        processing_thread.daemon = True
        processing_thread.start()
        
        return jsonify({"message": "Email processing started successfully"})
        
    except Exception as e:
        processing_status["errors"].append(str(e))
        return jsonify({"error": str(e)}), 500

@agent_bp.route('/stop', methods=['POST'])
def stop_processing():
    """Stop email processing"""
    try:
        global email_processor, processing_status
        
        if not processing_status["is_running"]:
            return jsonify({"message": "Email processing is not running"}), 400
        
        if email_processor:
            email_processor.stop_processing()
        
        processing_status["is_running"] = False
        
        return jsonify({"message": "Email processing stopped successfully"})
        
    except Exception as e:
        processing_status["errors"].append(str(e))
        return jsonify({"error": str(e)}), 500

@agent_bp.route('/authenticate', methods=['POST'])
def authenticate():
    """Initiate authentication with Microsoft Graph API"""
    try:
        global email_processor
        
        # Initialize processor if not already done
        if not email_processor:
            processor, error = initialize_email_processor()
            if error:
                return jsonify({"error": error}), 500
            email_processor = processor
        
        # Start authentication flow
        success = email_processor.graph_client.authenticate()
        
        if success:
            # Save credentials
            credentials_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data", "graph_credentials.json")
            os.makedirs(os.path.dirname(credentials_file), exist_ok=True)
            email_processor.graph_client.save_credentials(credentials_file)
            
            return jsonify({"message": "Authentication successful"})
        else:
            return jsonify({"error": "Authentication failed"}), 401
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@agent_bp.route('/config', methods=['GET'])
def get_config():
    """Get current configuration"""
    try:
        config = {
            "user_email": AgentConfig.USER_EMAIL,
            "user_name": AgentConfig.USER_NAME,
            "company_domain": AgentConfig.COMPANY_DOMAIN,
            "email_processing": AgentConfig.EMAIL_PROCESSING,
            "auto_response_config": AgentConfig.AUTO_RESPONSE_CONFIG,
            "scheduling_config": AgentConfig.SCHEDULING_CONFIG
        }
        
        return jsonify(config)
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@agent_bp.route('/config', methods=['POST'])
def update_config():
    """Update configuration"""
    try:
        data = request.get_json()
        
        # Update configuration (in a real app, this would persist to file)
        if 'email_processing' in data:
            AgentConfig.EMAIL_PROCESSING.update(data['email_processing'])
            # Also update the processor if it exists
            if email_processor:
                email_processor.auto_response_enabled = AgentConfig.EMAIL_PROCESSING.get('auto_response_enabled', False)
        
        if 'auto_response_config' in data:
            AgentConfig.AUTO_RESPONSE_CONFIG.update(data['auto_response_config'])
        
        return jsonify({"message": "Configuration updated successfully"})
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@agent_bp.route('/pending-responses', methods=['GET'])
def get_pending_responses():
    """Get all pending email responses awaiting approval"""
    try:
        global email_processor
        
        if not email_processor:
            return jsonify({"pending_responses": []})
        
        pending = email_processor.get_pending_responses()
        
        return jsonify({
            "pending_responses": pending,
            "count": len(pending)
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@agent_bp.route('/pending-responses/<response_id>/approve', methods=['POST'])
def approve_response(response_id):
    """Approve and send a pending response"""
    try:
        global email_processor
        
        if not email_processor:
            return jsonify({"error": "Email processor not initialized"}), 400
        
        success = email_processor.approve_response(response_id)
        
        if success:
            return jsonify({"message": "Response approved and sent successfully"})
        else:
            return jsonify({"error": "Failed to approve response"}), 400
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@agent_bp.route('/pending-responses/<response_id>/reject', methods=['POST'])
def reject_response(response_id):
    """Reject a pending response"""
    try:
        global email_processor
        
        if not email_processor:
            return jsonify({"error": "Email processor not initialized"}), 400
        
        success = email_processor.reject_response(response_id)
        
        if success:
            return jsonify({"message": "Response rejected successfully"})
        else:
            return jsonify({"error": "Failed to reject response"}), 400
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@agent_bp.route('/recent-emails', methods=['GET'])
def get_recent_emails():
    """Get recently processed emails"""
    try:
        global email_processor
        
        if not email_processor:
            return jsonify({"emails": []})
        
        # Get recent processed emails
        recent_emails = []
        for processed_email in email_processor.processed_emails[-10:]:  # Last 10
            email_data = {
                "id": processed_email.message.id,
                "subject": processed_email.message.subject,
                "sender": processed_email.message.sender,
                "received_datetime": processed_email.message.received_datetime.isoformat(),
                "priority_score": processed_email.analysis.summary.priority_score,
                "tasks_count": len(processed_email.analysis.tasks),
                "insights_count": len(processed_email.analysis.insights),
                "meeting_requests_count": len(processed_email.analysis.meeting_requests),
                "actions_taken": processed_email.actions_taken,
                "processed_at": processed_email.processed_at.isoformat()
            }
            recent_emails.append(email_data)
        
        return jsonify({"emails": recent_emails})
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@agent_bp.route('/analytics', methods=['GET'])
def get_analytics():
    """Get email processing analytics"""
    try:
        global email_processor
        
        if not email_processor or not email_processor.processed_emails:
            return jsonify({
                "total_processed": 0,
                "priority_distribution": {},
                "actions_summary": {},
                "processing_trends": []
            })
        
        processed_emails = email_processor.processed_emails
        
        # Priority distribution
        priority_distribution = {}
        for email in processed_emails:
            priority = int(email.analysis.summary.priority_score)
            priority_range = f"{priority}-{priority+1}"
            priority_distribution[priority_range] = priority_distribution.get(priority_range, 0) + 1
        
        # Actions summary
        actions_summary = {}
        for email in processed_emails:
            for action in email.actions_taken:
                actions_summary[action] = actions_summary.get(action, 0) + 1
        
        # Processing trends (last 7 days)
        trends = []
        for i in range(7):
            date = datetime.now() - timedelta(days=i)
            date_str = date.strftime("%Y-%m-%d")
            
            count = sum(1 for email in processed_emails 
                       if email.processed_at.date() == date.date())
            
            trends.append({
                "date": date_str,
                "count": count
            })
        
        trends.reverse()  # Oldest first
        
        analytics = {
            "total_processed": len(processed_emails),
            "priority_distribution": priority_distribution,
            "actions_summary": actions_summary,
            "processing_trends": trends,
            "average_priority": sum(email.analysis.summary.priority_score for email in processed_emails) / len(processed_emails),
            "total_tasks_created": sum(len(email.analysis.tasks) for email in processed_emails),
            "total_meetings_processed": sum(len(email.analysis.meeting_requests) for email in processed_emails)
        }
        
        return jsonify(analytics)
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@agent_bp.route('/test-demo', methods=['POST'])
def run_demo():
    """Run demo with sample emails"""
    try:
        if not EMAIL_MODULES_AVAILABLE:
            # Simplified demo without full modules
            results = [
                {
                    "subject": "Q4 Budget Review - Need Your Input by Friday",
                    "sender": "<EMAIL>",
                    "priority_score": 10.0,
                    "tasks_count": 5,
                    "insights_count": 3,
                    "meeting_requests_count": 0,
                    "has_auto_response": True
                },
                {
                    "subject": "Team Meeting Recap and Action Items",
                    "sender": "<EMAIL>",
                    "priority_score": 5.0,
                    "tasks_count": 2,
                    "insights_count": 1,
                    "meeting_requests_count": 1,
                    "has_auto_response": True
                },
                {
                    "subject": "Urgent: Client Meeting Moved to Tomorrow",
                    "sender": "<EMAIL>",
                    "priority_score": 10.0,
                    "tasks_count": 2,
                    "insights_count": 3,
                    "meeting_requests_count": 1,
                    "has_auto_response": True
                },
                {
                    "subject": "Invoice #12345 - Payment Overdue",
                    "sender": "<EMAIL>",
                    "priority_score": 5.0,
                    "tasks_count": 0,
                    "insights_count": 1,
                    "meeting_requests_count": 0,
                    "has_auto_response": True
                }
            ]
            
            return jsonify({
                "message": "Demo completed successfully (simplified mode)",
                "results": results
            })
        
        # Full demo with actual modules
        from core.mock_data import get_all_sample_emails
        from integrated_agent import IntegratedEmailAgent
        
        agent = IntegratedEmailAgent()
        sample_emails = get_all_sample_emails()
        
        results = []
        for email in sample_emails:
            result = agent.analyze_email(email, generate_draft=True)
            
            email_result = {
                "subject": email["subject"],
                "sender": email["sender"],
                "priority_score": result.summary.priority_score,
                "tasks_count": len(result.tasks),
                "insights_count": len(result.insights),
                "meeting_requests_count": len(result.meeting_requests),
                "has_auto_response": bool(result.suggested_draft)
            }
            results.append(email_result)
        
        return jsonify({
            "message": "Demo completed successfully",
            "results": results
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500
